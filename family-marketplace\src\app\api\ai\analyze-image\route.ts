import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { AIService } from '@/lib/openai'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { imageUrl } = body

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      )
    }

    // Validate that the image URL is accessible
    try {
      const imageResponse = await fetch(imageUrl, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })
      if (!imageResponse.ok) {
        console.log('Image fetch failed:', imageResponse.status, imageResponse.statusText)
        // Don't fail here - let OpenAI try to access it directly
      }
    } catch (error) {
      console.log('Image validation error:', error)
      // Don't fail here - let OpenAI try to access it directly
    }

    // Analyze the image with AI
    console.log('Analyzing image URL:', imageUrl)
    const analysis = await AIService.analyzeImage(imageUrl)

    // Generate price suggestion based on the analysis
    let priceSuggestion = null
    try {
      priceSuggestion = await AIService.suggestPrice({
        category: analysis.category,
        subcategory: analysis.subcategory,
        brand: analysis.brand,
        model: analysis.model,
        condition: analysis.condition,
        description: analysis.description
      })
    } catch (error) {
      console.error('Price suggestion failed:', error)
      // Continue without price suggestion
    }

    return NextResponse.json({
      success: true,
      analysis: {
        ...analysis,
        priceSuggestion
      }
    })
  } catch (error) {
    console.error('AI image analysis error:', error)
    
    // Return a more specific error message
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error' },
          { status: 500 }
        )
      }
      if (error.message.includes('quota') || error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'AI service temporarily unavailable. Please try again later.' },
          { status: 429 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to analyze image. Please try again.' },
      { status: 500 }
    )
  }
}

// GET endpoint to check AI service status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if OpenAI API key is configured
    const isConfigured = !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your-openai-api-key-here'

    return NextResponse.json({
      success: true,
      aiEnabled: isConfigured,
      features: {
        imageAnalysis: isConfigured,
        priceSuggestion: isConfigured,
        descriptionEnhancement: isConfigured
      }
    })
  } catch (error) {
    console.error('AI status check error:', error)
    return NextResponse.json(
      { error: 'Failed to check AI service status' },
      { status: 500 }
    )
  }
}
