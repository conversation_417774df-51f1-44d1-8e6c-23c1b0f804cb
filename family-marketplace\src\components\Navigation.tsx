'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useSession, signOut } from 'next-auth/react'
import { usePathname } from 'next/navigation'

export default function Navigation() {
  const { data: session, status } = useSession()
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)

  const isActive = (path: string) => pathname === path

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/logo.svg"
              alt="Family Marketplace"
              width={200}
              height={50}
              className="h-10 w-auto"
              priority
            />
            <span className="ml-3 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
              AI-Powered
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link 
              href="/" 
              className={`text-gray-600 hover:text-gray-900 transition-colors ${
                isActive('/') ? 'text-blue-600 font-medium' : ''
              }`}
            >
              Home
            </Link>
            
            <Link 
              href="/browse" 
              className={`text-gray-600 hover:text-gray-900 transition-colors ${
                isActive('/browse') ? 'text-blue-600 font-medium' : ''
              }`}
            >
              Browse
            </Link>
            
            {session ? (
              <Link
                href="/create"
                className={`bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-105 ${
                  isActive('/create') ? 'ring-2 ring-purple-300' : ''
                }`}
              >
                🤖 Sell with AI
              </Link>
            ) : (
              <Link
                href="/login?message=Please+sign+in+to+create+a+listing&redirect=%2Fcreate"
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-105"
              >
                🤖 Sell with AI
              </Link>
            )}

            {/* User Menu */}
            {status === 'loading' ? (
              <div className="animate-pulse bg-gray-200 h-8 w-20 rounded"></div>
            ) : session ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                    {session.user.firstName ? session.user.firstName[0] : session.user.username[0].toUpperCase()}
                  </div>
                  <span className="hidden lg:block">{session.user.firstName || session.user.username}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* User Dropdown */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">
                        {session.user.firstName} {session.user.lastName}
                      </p>
                      <p className="text-xs text-gray-500">{session.user.email}</p>
                      <div className="flex items-center mt-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          session.user.userType === 'VENDOR' 
                            ? 'bg-purple-100 text-purple-800' 
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {session.user.userType === 'VENDOR' ? '🏪 Vendor' : '👤 Private'}
                        </span>
                      </div>
                    </div>
                    
                    <Link href="/profile" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                      <span className="mr-3">👤</span>
                      My Profile
                    </Link>
                    
                    <Link href="/listings" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                      <span className="mr-3">📝</span>
                      My Listings
                    </Link>
                    
                    {session.user.userType === 'VENDOR' && (
                      <Link href="/dashboard" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <span className="mr-3">📊</span>
                        Vendor Dashboard
                      </Link>
                    )}

                    {session.user.userType === 'ADMIN' && (
                      <Link href="/admin" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-red-50">
                        <span className="mr-3">🛡️</span>
                        Admin Dashboard
                      </Link>
                    )}
                    
                    <Link href="/messages" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                      <span className="mr-3">💬</span>
                      Messages
                    </Link>
                    
                    <Link href="/favorites" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                      <span className="mr-3">❤️</span>
                      Favorites
                    </Link>
                    
                    <div className="border-t border-gray-100 mt-2 pt-2">
                      <Link href="/settings" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <span className="mr-3">⚙️</span>
                        Settings
                      </Link>
                      
                      <button
                        onClick={handleSignOut}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <span className="mr-3">🚪</span>
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link 
                  href="/login" 
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  Sign In
                </Link>
                
                <div className="relative group">
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-all">
                    Join Now
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                    <div className="py-2">
                      <Link href="/register?type=private" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50">
                        <span className="mr-2">👤</span>
                        Private Seller
                      </Link>
                      <Link href="/register?type=vendor" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-purple-50">
                        <span className="mr-2">🏪</span>
                        Business Vendor
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-600 hover:text-gray-900 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <div className="space-y-2">
              <Link 
                href="/" 
                className={`block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors ${
                  isActive('/') ? 'text-blue-600 bg-blue-50 font-medium' : ''
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Home
              </Link>
              
              <Link 
                href="/browse" 
                className={`block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors ${
                  isActive('/browse') ? 'text-blue-600 bg-blue-50 font-medium' : ''
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Browse
              </Link>
              
              {session ? (
                <Link
                  href="/create"
                  className={`block px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-semibold ${
                    isActive('/create') ? 'ring-2 ring-purple-300' : ''
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  🤖 Sell with AI
                </Link>
              ) : (
                <Link
                  href="/login?message=Please+sign+in+to+create+a+listing&redirect=%2Fcreate"
                  className="block px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-semibold"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  🤖 Sell with AI
                </Link>
              )}

              {session ? (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="px-4 py-2">
                    <p className="text-sm font-medium text-gray-900">
                      {session.user.firstName} {session.user.lastName}
                    </p>
                    <p className="text-xs text-gray-500">{session.user.email}</p>
                  </div>
                  
                  <Link href="/profile" className="block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                    👤 My Profile
                  </Link>
                  <Link href="/listings" className="block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                    📝 My Listings
                  </Link>
                  {session.user.userType === 'VENDOR' && (
                    <Link href="/dashboard" className="block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                      📊 Vendor Dashboard
                    </Link>
                  )}
                  {session.user.userType === 'ADMIN' && (
                    <Link href="/admin" className="block px-4 py-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg">
                      🛡️ Admin Dashboard
                    </Link>
                  )}
                  <Link href="/messages" className="block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                    💬 Messages
                  </Link>
                  <Link href="/favorites" className="block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                    ❤️ Favorites
                  </Link>
                  <Link href="/settings" className="block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                    ⚙️ Settings
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    🚪 Sign Out
                  </button>
                </div>
              ) : (
                <div className="border-t border-gray-200 pt-4 mt-4 space-y-2">
                  <Link 
                    href="/login" 
                    className="block px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link 
                    href="/register?type=private" 
                    className="block px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    👤 Join as Private Seller
                  </Link>
                  <Link 
                    href="/register?type=vendor" 
                    className="block px-4 py-2 text-purple-600 hover:bg-purple-50 rounded-lg"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    🏪 Join as Vendor
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close dropdowns */}
      {(isUserMenuOpen || isMobileMenuOpen) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setIsUserMenuOpen(false)
            setIsMobileMenuOpen(false)
          }}
        />
      )}
    </nav>
  )
}
