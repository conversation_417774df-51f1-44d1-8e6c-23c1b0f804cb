import Link from "next/link";

export default function Home() {
  return (
    <div className="bg-gradient-to-br from-blue-50 to-purple-50">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            The Future of
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">
              {" "}AI-Powered{" "}
            </span>
            Marketplace
          </h1>

          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Snap a photo, and let AI create your perfect listing. Smart categorization,
            intelligent pricing, and automated descriptions make selling effortless.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link
              href="/create"
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-105"
            >
              🤖 Create AI Listing
            </Link>
            <Link
              href="/browse"
              className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-lg font-semibold text-lg hover:border-gray-400 hover:bg-gray-50 transition-all"
            >
              Browse Items
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-4xl mb-4">📸</div>
            <h3 className="text-xl font-semibold mb-2">Smart Photo Analysis</h3>
            <p className="text-gray-600">
              Upload a photo and AI instantly identifies category, brand, condition, and suggests the perfect title and description.
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-4xl mb-4">💰</div>
            <h3 className="text-xl font-semibold mb-2">Intelligent Pricing</h3>
            <p className="text-gray-600">
              AI analyzes market data to suggest competitive prices based on item condition, brand, and current demand.
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-4xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold mb-2">Visual Search</h3>
            <p className="text-gray-600">
              Find similar items by uploading a photo. Perfect for price comparison and discovering alternatives.
            </p>
          </div>
        </div>

        {/* Demo Section */}
        <div className="bg-white rounded-2xl p-8 shadow-xl">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              See AI in Action
            </h2>
            <p className="text-gray-600">
              Experience the magic of AI-powered listing creation
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="bg-purple-100 rounded-full p-2 flex-shrink-0">
                  <span className="text-purple-600 font-bold">1</span>
                </div>
                <div>
                  <h4 className="font-semibold">Upload Your Photo</h4>
                  <p className="text-gray-600 text-sm">Simply drag and drop or click to upload product images</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-purple-100 rounded-full p-2 flex-shrink-0">
                  <span className="text-purple-600 font-bold">2</span>
                </div>
                <div>
                  <h4 className="font-semibold">AI Analyzes Everything</h4>
                  <p className="text-gray-600 text-sm">Our AI identifies category, brand, condition, and creates descriptions</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-purple-100 rounded-full p-2 flex-shrink-0">
                  <span className="text-purple-600 font-bold">3</span>
                </div>
                <div>
                  <h4 className="font-semibold">Smart Price Suggestions</h4>
                  <p className="text-gray-600 text-sm">Get competitive pricing based on market analysis</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-purple-100 rounded-full p-2 flex-shrink-0">
                  <span className="text-purple-600 font-bold">4</span>
                </div>
                <div>
                  <h4 className="font-semibold">Publish & Sell</h4>
                  <p className="text-gray-600 text-sm">Review, adjust, and publish your optimized listing</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl p-6 text-center">
              <div className="text-6xl mb-4">🤖</div>
              <h3 className="text-xl font-semibold mb-2">Ready to try it?</h3>
              <p className="text-gray-600 mb-4">
                Create your first AI-powered listing in under 2 minutes
              </p>
              <Link
                href="/create"
                className="inline-block bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all"
              >
                Start Creating →
              </Link>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">95%</div>
            <div className="text-gray-600">Accuracy Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">2min</div>
            <div className="text-gray-600">Average Listing Time</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">30%</div>
            <div className="text-gray-600">Faster Sales</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">24/7</div>
            <div className="text-gray-600">AI Assistant</div>
          </div>
        </div>
      </div>
    </div>
  );
}
