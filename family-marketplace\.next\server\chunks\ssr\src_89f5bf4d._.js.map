{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode, useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { usePathname } from 'next/navigation'\nimport Link from 'next/link'\nimport { \n  Home, \n  Package, \n  BarChart3, \n  Users, \n  Settings, \n  Heart, \n  MessageCircle, \n  Plus,\n  Menu,\n  X,\n  Shield,\n  Store,\n  User,\n  Bell,\n  Search,\n  LogOut\n} from 'lucide-react'\nimport { signOut } from 'next-auth/react'\n\ninterface DashboardLayoutProps {\n  children: ReactNode\n  userType: 'PRIVATE' | 'VENDOR' | 'ADMIN'\n}\n\ninterface NavItem {\n  name: string\n  href: string\n  icon: any\n  badge?: number\n}\n\nexport default function DashboardLayout({ children, userType }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const pathname = usePathname()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const getNavigationItems = (): NavItem[] => {\n    const baseItems: NavItem[] = [\n      { name: 'Overview', href: getDashboardPath(), icon: Home },\n      { name: 'My Listings', href: '/listings', icon: Package },\n      { name: 'Messages', href: '/messages', icon: MessageCircle, badge: 3 },\n      { name: 'Favorites', href: '/favorites', icon: Heart },\n    ]\n\n    if (userType === 'PRIVATE') {\n      return [\n        ...baseItems,\n        { name: 'Create Listing', href: '/create', icon: Plus },\n        { name: 'Profile', href: '/profile', icon: User },\n        { name: 'Settings', href: '/settings', icon: Settings },\n      ]\n    }\n\n    if (userType === 'VENDOR') {\n      return [\n        ...baseItems,\n        { name: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 },\n        { name: 'Create Listing', href: '/create', icon: Plus },\n        { name: 'Store Profile', href: '/dashboard/store', icon: Store },\n        { name: 'Settings', href: '/settings', icon: Settings },\n      ]\n    }\n\n    if (userType === 'ADMIN') {\n      return [\n        { name: 'Overview', href: '/admin', icon: Home },\n        { name: 'Users', href: '/admin/users', icon: Users },\n        { name: 'Listings', href: '/admin/listings', icon: Package },\n        { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },\n        { name: 'AI Monitoring', href: '/admin/ai', icon: Shield },\n        { name: 'Settings', href: '/admin/settings', icon: Settings },\n      ]\n    }\n\n    return baseItems\n  }\n\n  const getDashboardPath = () => {\n    switch (userType) {\n      case 'ADMIN': return '/admin'\n      case 'VENDOR': return '/dashboard'\n      case 'PRIVATE': return '/dashboard/private'\n      default: return '/dashboard'\n    }\n  }\n\n  const getDashboardTitle = () => {\n    switch (userType) {\n      case 'ADMIN': return 'Admin Dashboard'\n      case 'VENDOR': return 'Vendor Dashboard'\n      case 'PRIVATE': return 'My Dashboard'\n      default: return 'Dashboard'\n    }\n  }\n\n  const getUserTypeColor = () => {\n    switch (userType) {\n      case 'ADMIN': return 'bg-red-100 text-red-800'\n      case 'VENDOR': return 'bg-purple-100 text-purple-800'\n      case 'PRIVATE': return 'bg-blue-100 text-blue-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getUserTypeIcon = () => {\n    switch (userType) {\n      case 'ADMIN': return '🛡️'\n      case 'VENDOR': return '🏪'\n      case 'PRIVATE': return '👤'\n      default: return '👤'\n    }\n  }\n\n  const navigationItems = getNavigationItems()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">{getDashboardTitle()}</h2>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4\">\n            <ul className=\"space-y-2\">\n              {navigationItems.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                      pathname === item.href\n                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                        : 'text-gray-700 hover:bg-gray-50'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" />\n                    {item.name}\n                    {item.badge && (\n                      <span className=\"ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1\">\n                        {item.badge}\n                      </span>\n                    )}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm\">\n          <div className=\"flex items-center h-16 px-4 border-b border-gray-200\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm\">\n                FM\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Family Market</span>\n            </Link>\n          </div>\n          \n          <div className=\"flex-1 flex flex-col\">\n            <nav className=\"flex-1 px-4 py-4\">\n              <ul className=\"space-y-2\">\n                {navigationItems.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                        pathname === item.href\n                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                          : 'text-gray-700 hover:bg-gray-50'\n                      }`}\n                    >\n                      <item.icon className=\"mr-3 h-5 w-5\" />\n                      {item.name}\n                      {item.badge && (\n                        <span className=\"ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1\">\n                          {item.badge}\n                        </span>\n                      )}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            {/* User info */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <div className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold\">\n                  {session?.user?.firstName?.[0] || session?.user?.username?.[0] || 'U'}\n                </div>\n                <div className=\"ml-3 flex-1\">\n                  <p className=\"text-sm font-medium text-gray-900\">\n                    {session?.user?.firstName || session?.user?.username}\n                  </p>\n                  <div className=\"flex items-center mt-1\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUserTypeColor()}`}>\n                      {getUserTypeIcon()} {userType}\n                    </span>\n                  </div>\n                </div>\n                <button\n                  onClick={() => signOut()}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                  title=\"Sign out\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"text-gray-500 hover:text-gray-600 lg:hidden\"\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search...\"\n                  className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <button className=\"relative text-gray-400 hover:text-gray-600\">\n                <Bell className=\"h-6 w-6\" />\n                <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\">\n                  3\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;;AAsCe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAwB;IAClF,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB;QACzB,MAAM,YAAuB;YAC3B;gBAAE,MAAM;gBAAY,MAAM;gBAAoB,MAAM,mMAAA,CAAA,OAAI;YAAC;YACzD;gBAAE,MAAM;gBAAe,MAAM;gBAAa,MAAM,wMAAA,CAAA,UAAO;YAAC;YACxD;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,wNAAA,CAAA,gBAAa;gBAAE,OAAO;YAAE;YACrE;gBAAE,MAAM;gBAAa,MAAM;gBAAc,MAAM,oMAAA,CAAA,QAAK;YAAC;SACtD;QAED,IAAI,aAAa,WAAW;YAC1B,OAAO;mBACF;gBACH;oBAAE,MAAM;oBAAkB,MAAM;oBAAW,MAAM,kMAAA,CAAA,OAAI;gBAAC;gBACtD;oBAAE,MAAM;oBAAW,MAAM;oBAAY,MAAM,kMAAA,CAAA,OAAI;gBAAC;gBAChD;oBAAE,MAAM;oBAAY,MAAM;oBAAa,MAAM,0MAAA,CAAA,WAAQ;gBAAC;aACvD;QACH;QAEA,IAAI,aAAa,UAAU;YACzB,OAAO;mBACF;gBACH;oBAAE,MAAM;oBAAa,MAAM;oBAAwB,MAAM,kNAAA,CAAA,YAAS;gBAAC;gBACnE;oBAAE,MAAM;oBAAkB,MAAM;oBAAW,MAAM,kMAAA,CAAA,OAAI;gBAAC;gBACtD;oBAAE,MAAM;oBAAiB,MAAM;oBAAoB,MAAM,oMAAA,CAAA,QAAK;gBAAC;gBAC/D;oBAAE,MAAM;oBAAY,MAAM;oBAAa,MAAM,0MAAA,CAAA,WAAQ;gBAAC;aACvD;QACH;QAEA,IAAI,aAAa,SAAS;YACxB,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;oBAAU,MAAM,mMAAA,CAAA,OAAI;gBAAC;gBAC/C;oBAAE,MAAM;oBAAS,MAAM;oBAAgB,MAAM,oMAAA,CAAA,QAAK;gBAAC;gBACnD;oBAAE,MAAM;oBAAY,MAAM;oBAAmB,MAAM,wMAAA,CAAA,UAAO;gBAAC;gBAC3D;oBAAE,MAAM;oBAAa,MAAM;oBAAoB,MAAM,kNAAA,CAAA,YAAS;gBAAC;gBAC/D;oBAAE,MAAM;oBAAiB,MAAM;oBAAa,MAAM,sMAAA,CAAA,SAAM;gBAAC;gBACzD;oBAAE,MAAM;oBAAY,MAAM;oBAAmB,MAAM,0MAAA,CAAA,WAAQ;gBAAC;aAC7D;QACH;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB;IAExB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,6EAA6E,EACvF,aAAa,KAAK,IAAI,GAClB,wDACA,kCACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;oDACT,KAAK,KAAK,kBACT,8OAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;;;;;;;2CAdV,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA0B5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAAgI;;;;;;kDAG/I,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDACX,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAC,6EAA6E,EACvF,aAAa,KAAK,IAAI,GAClB,wDACA,kCACJ;;sEAEF,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;wDACpB,KAAK,IAAI;wDACT,KAAK,KAAK,kBACT,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;;;;;;;+CAbV,KAAK,IAAI;;;;;;;;;;;;;;;8CAuBxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,WAAW,CAAC,EAAE,IAAI,SAAS,MAAM,UAAU,CAAC,EAAE,IAAI;;;;;;0DAEpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,SAAS,MAAM,aAAa,SAAS,MAAM;;;;;;kEAE9C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAW,CAAC,oEAAoE,EAAE,oBAAoB;;gEACzG;gEAAkB;gEAAE;;;;;;;;;;;;;;;;;;0DAI3B,8OAAC;gDACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;gDACrB,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAA+G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvI,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/dashboard/StatsCard.tsx"], "sourcesContent": ["import { ReactNode } from 'react'\nimport { TrendingUp, TrendingDown } from 'lucide-react'\n\ninterface StatsCardProps {\n  title: string\n  value: string | number\n  change?: {\n    value: number\n    type: 'increase' | 'decrease'\n    period: string\n  }\n  icon?: ReactNode\n  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'gray'\n  subtitle?: string\n  loading?: boolean\n}\n\nexport default function StatsCard({ \n  title, \n  value, \n  change, \n  icon, \n  color = 'blue', \n  subtitle,\n  loading = false \n}: StatsCardProps) {\n  const getColorClasses = () => {\n    switch (color) {\n      case 'blue':\n        return 'bg-blue-500 text-white'\n      case 'green':\n        return 'bg-green-500 text-white'\n      case 'purple':\n        return 'bg-purple-500 text-white'\n      case 'orange':\n        return 'bg-orange-500 text-white'\n      case 'red':\n        return 'bg-red-500 text-white'\n      case 'gray':\n        return 'bg-gray-500 text-white'\n      default:\n        return 'bg-blue-500 text-white'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm p-6 border border-gray-100\">\n        <div className=\"animate-pulse\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-gray-200 rounded-lg\"></div>\n            <div className=\"ml-4 flex-1\">\n              <div className=\"h-4 bg-gray-200 rounded w-24 mb-2\"></div>\n              <div className=\"h-8 bg-gray-200 rounded w-16 mb-2\"></div>\n              <div className=\"h-3 bg-gray-200 rounded w-20\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow\">\n      <div className=\"flex items-center\">\n        {icon && (\n          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses()}`}>\n            {icon}\n          </div>\n        )}\n        <div className={`${icon ? 'ml-4' : ''} flex-1`}>\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900 mb-1\">\n            {typeof value === 'number' ? value.toLocaleString() : value}\n          </p>\n          \n          <div className=\"flex items-center space-x-2\">\n            {change && (\n              <div className={`flex items-center text-sm ${\n                change.type === 'increase' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {change.type === 'increase' ? (\n                  <TrendingUp className=\"w-4 h-4 mr-1\" />\n                ) : (\n                  <TrendingDown className=\"w-4 h-4 mr-1\" />\n                )}\n                <span className=\"font-medium\">\n                  {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%\n                </span>\n                <span className=\"text-gray-500 ml-1\">{change.period}</span>\n              </div>\n            )}\n            \n            {subtitle && !change && (\n              <p className=\"text-sm text-gray-500\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Quick stats grid component\ninterface QuickStatsProps {\n  stats: Array<{\n    title: string\n    value: string | number\n    change?: {\n      value: number\n      type: 'increase' | 'decrease'\n      period: string\n    }\n    icon?: ReactNode\n    color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'gray'\n    subtitle?: string\n  }>\n  loading?: boolean\n}\n\nexport function QuickStats({ stats, loading = false }: QuickStatsProps) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {stats.map((stat, index) => (\n        <StatsCard\n          key={index}\n          title={stat.title}\n          value={stat.value}\n          change={stat.change}\n          icon={stat.icon}\n          color={stat.color}\n          subtitle={stat.subtitle}\n          loading={loading}\n        />\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;;;AAgBe,SAAS,UAAU,EAChC,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,QAAQ,MAAM,EACd,QAAQ,EACR,UAAU,KAAK,EACA;IACf,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;gBACZ,sBACC,8OAAC;oBAAI,WAAW,CAAC,sDAAsD,EAAE,mBAAmB;8BACzF;;;;;;8BAGL,8OAAC;oBAAI,WAAW,GAAG,OAAO,SAAS,GAAG,OAAO,CAAC;;sCAC5C,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCACV,OAAO,UAAU,WAAW,MAAM,cAAc,KAAK;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;;gCACZ,wBACC,8OAAC;oCAAI,WAAW,CAAC,0BAA0B,EACzC,OAAO,IAAI,KAAK,aAAa,mBAAmB,gBAChD;;wCACC,OAAO,IAAI,KAAK,2BACf,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;iEAEtB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDAE1B,8OAAC;4CAAK,WAAU;;gDACb,OAAO,IAAI,KAAK,aAAa,MAAM;gDAAK,KAAK,GAAG,CAAC,OAAO,KAAK;gDAAE;;;;;;;sDAElE,8OAAC;4CAAK,WAAU;sDAAsB,OAAO,MAAM;;;;;;;;;;;;gCAItD,YAAY,CAAC,wBACZ,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;AAmBO,SAAS,WAAW,EAAE,KAAK,EAAE,UAAU,KAAK,EAAmB;IACpE,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAEC,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,SAAS;eAPJ;;;;;;;;;;AAYf", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/dashboard/ChartCard.tsx"], "sourcesContent": ["import { ReactNode } from 'react'\nimport { BarChart3, TrendingUp, Download, MoreHorizontal } from 'lucide-react'\n\ninterface ChartCardProps {\n  title: string\n  subtitle?: string\n  children: ReactNode\n  actions?: ReactNode\n  loading?: boolean\n  height?: string\n}\n\nexport default function ChartCard({ \n  title, \n  subtitle, \n  children, \n  actions,\n  loading = false,\n  height = 'h-80'\n}: ChartCardProps) {\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-100\">\n        <div className=\"p-6 border-b border-gray-100\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-6 bg-gray-200 rounded w-32 mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-48\"></div>\n          </div>\n        </div>\n        <div className={`p-6 ${height}`}>\n          <div className=\"animate-pulse h-full bg-gray-200 rounded\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-100\">\n      <div className=\"p-6 border-b border-gray-100\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n            {subtitle && (\n              <p className=\"text-sm text-gray-600 mt-1\">{subtitle}</p>\n            )}\n          </div>\n          {actions && (\n            <div className=\"flex items-center space-x-2\">\n              {actions}\n            </div>\n          )}\n        </div>\n      </div>\n      <div className={`p-6 ${height}`}>\n        {children}\n      </div>\n    </div>\n  )\n}\n\n// Placeholder chart component for when no chart library is available\nexport function PlaceholderChart({ \n  type = 'bar', \n  title = 'Chart', \n  description = 'Chart visualization would appear here' \n}: {\n  type?: 'bar' | 'line' | 'pie' | 'area'\n  title?: string\n  description?: string\n}) {\n  const getIcon = () => {\n    switch (type) {\n      case 'bar':\n        return <BarChart3 className=\"w-12 h-12 text-gray-400\" />\n      case 'line':\n        return <TrendingUp className=\"w-12 h-12 text-gray-400\" />\n      default:\n        return <BarChart3 className=\"w-12 h-12 text-gray-400\" />\n    }\n  }\n\n  return (\n    <div className=\"h-full flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-200\">\n      <div className=\"text-center\">\n        {getIcon()}\n        <p className=\"text-gray-600 font-medium mt-2\">{title}</p>\n        <p className=\"text-sm text-gray-500 mt-1\">{description}</p>\n        <p className=\"text-xs text-gray-400 mt-2\">Chart library integration needed</p>\n      </div>\n    </div>\n  )\n}\n\n// Quick action buttons for charts\nexport function ChartActions() {\n  return (\n    <>\n      <button className=\"text-gray-400 hover:text-gray-600 p-1\">\n        <Download className=\"w-5 h-5\" />\n      </button>\n      <button className=\"text-gray-400 hover:text-gray-600 p-1\">\n        <MoreHorizontal className=\"w-5 h-5\" />\n      </button>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA;AAAA;AAAA;AAAA;;;AAWe,SAAS,UAAU,EAChC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,UAAU,KAAK,EACf,SAAS,MAAM,EACA;IACf,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;8BAGnB,8OAAC;oBAAI,WAAW,CAAC,IAAI,EAAE,QAAQ;8BAC7B,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;gCACpD,0BACC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;wBAG9C,yBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAKT,8OAAC;gBAAI,WAAW,CAAC,IAAI,EAAE,QAAQ;0BAC5B;;;;;;;;;;;;AAIT;AAGO,SAAS,iBAAiB,EAC/B,OAAO,KAAK,EACZ,QAAQ,OAAO,EACf,cAAc,uCAAuC,EAKtD;IACC,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,8OAAC,kNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;gBACZ;8BACD,8OAAC;oBAAE,WAAU;8BAAkC;;;;;;8BAC/C,8OAAC;oBAAE,WAAU;8BAA8B;;;;;;8BAC3C,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;AAGO,SAAS;IACd,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAEtB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC,gNAAA,CAAA,iBAAc;oBAAC,WAAU;;;;;;;;;;;;;AAIlC", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/dashboard/ActivityFeed.tsx"], "sourcesContent": ["import { ReactNode } from 'react'\nimport Link from 'next/link'\nimport { formatDistanceToNow } from 'date-fns'\n\ninterface ActivityItem {\n  id: string\n  type: 'listing' | 'message' | 'sale' | 'user' | 'review' | 'favorite'\n  title: string\n  description?: string\n  timestamp: string\n  user?: {\n    name: string\n    avatar?: string\n    userType?: string\n  }\n  metadata?: {\n    price?: number\n    status?: string\n    rating?: number\n  }\n  href?: string\n}\n\ninterface ActivityFeedProps {\n  title: string\n  items: ActivityItem[]\n  loading?: boolean\n  emptyMessage?: string\n  showAll?: boolean\n  maxItems?: number\n}\n\nexport default function ActivityFeed({ \n  title, \n  items, \n  loading = false, \n  emptyMessage = 'No recent activity',\n  showAll = false,\n  maxItems = 5\n}: ActivityFeedProps) {\n  const displayItems = showAll ? items : items.slice(0, maxItems)\n\n  const getActivityIcon = (type: string) => {\n    switch (type) {\n      case 'listing':\n        return '📝'\n      case 'message':\n        return '💬'\n      case 'sale':\n        return '💰'\n      case 'user':\n        return '👤'\n      case 'review':\n        return '⭐'\n      case 'favorite':\n        return '❤️'\n      default:\n        return '📋'\n    }\n  }\n\n  const getActivityColor = (type: string) => {\n    switch (type) {\n      case 'listing':\n        return 'bg-blue-100 text-blue-600'\n      case 'message':\n        return 'bg-green-100 text-green-600'\n      case 'sale':\n        return 'bg-yellow-100 text-yellow-600'\n      case 'user':\n        return 'bg-purple-100 text-purple-600'\n      case 'review':\n        return 'bg-orange-100 text-orange-600'\n      case 'favorite':\n        return 'bg-red-100 text-red-600'\n      default:\n        return 'bg-gray-100 text-gray-600'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-100\">\n        <div className=\"p-6 border-b border-gray-100\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-6 bg-gray-200 rounded w-32\"></div>\n          </div>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"animate-pulse flex items-start space-x-3\">\n                <div className=\"w-8 h-8 bg-gray-200 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-100\">\n      <div className=\"p-6 border-b border-gray-100\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n      </div>\n      <div className=\"p-6\">\n        {displayItems.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <div className=\"text-gray-400 text-4xl mb-2\">📋</div>\n            <p className=\"text-gray-500\">{emptyMessage}</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {displayItems.map((item) => (\n              <div key={item.id} className=\"flex items-start space-x-3\">\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${getActivityColor(item.type)}`}>\n                  {getActivityIcon(item.type)}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      {item.href ? (\n                        <Link href={item.href} className=\"text-sm font-medium text-gray-900 hover:text-blue-600\">\n                          {item.title}\n                        </Link>\n                      ) : (\n                        <p className=\"text-sm font-medium text-gray-900\">{item.title}</p>\n                      )}\n                      {item.description && (\n                        <p className=\"text-sm text-gray-600 mt-1\">{item.description}</p>\n                      )}\n                      <div className=\"flex items-center mt-2 space-x-4\">\n                        {item.user && (\n                          <div className=\"flex items-center text-xs text-gray-500\">\n                            <div className=\"w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs mr-1\">\n                              {item.user.name[0]}\n                            </div>\n                            {item.user.name}\n                          </div>\n                        )}\n                        {item.metadata?.price && (\n                          <span className=\"text-xs text-green-600 font-medium\">\n                            ${item.metadata.price.toLocaleString()}\n                          </span>\n                        )}\n                        {item.metadata?.status && (\n                          <span className={`text-xs px-2 py-1 rounded-full ${\n                            item.metadata.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :\n                            item.metadata.status === 'SOLD' ? 'bg-blue-100 text-blue-800' :\n                            'bg-gray-100 text-gray-800'\n                          }`}>\n                            {item.metadata.status}\n                          </span>\n                        )}\n                        {item.metadata?.rating && (\n                          <div className=\"flex items-center text-xs text-yellow-600\">\n                            ⭐ {item.metadata.rating}/5\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                    <span className=\"text-xs text-gray-500 ml-2 whitespace-nowrap\">\n                      {formatDistanceToNow(new Date(item.timestamp), { addSuffix: true })}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n        \n        {!showAll && items.length > maxItems && (\n          <div className=\"mt-4 text-center\">\n            <button className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\">\n              View all activity ({items.length})\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AA8Be,SAAS,aAAa,EACnC,KAAK,EACL,KAAK,EACL,UAAU,KAAK,EACf,eAAe,oBAAoB,EACnC,UAAU,KAAK,EACf,WAAW,CAAC,EACM;IAClB,MAAM,eAAe,UAAU,QAAQ,MAAM,KAAK,CAAC,GAAG;IAEtD,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAGnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAJT;;;;;;;;;;;;;;;;;;;;;IAYtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAuC;;;;;;;;;;;0BAEvD,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,MAAM,KAAK,kBACvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;6CAGhC,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC;wCAAI,WAAW,CAAC,8DAA8D,EAAE,iBAAiB,KAAK,IAAI,GAAG;kDAC3G,gBAAgB,KAAK,IAAI;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,KAAK,IAAI;4DAAE,WAAU;sEAC9B,KAAK,KAAK;;;;;iFAGb,8OAAC;4DAAE,WAAU;sEAAqC,KAAK,KAAK;;;;;;wDAE7D,KAAK,WAAW,kBACf,8OAAC;4DAAE,WAAU;sEAA8B,KAAK,WAAW;;;;;;sEAE7D,8OAAC;4DAAI,WAAU;;gEACZ,KAAK,IAAI,kBACR,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACZ,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;;;;;;wEAEnB,KAAK,IAAI,CAAC,IAAI;;;;;;;gEAGlB,KAAK,QAAQ,EAAE,uBACd,8OAAC;oEAAK,WAAU;;wEAAqC;wEACjD,KAAK,QAAQ,CAAC,KAAK,CAAC,cAAc;;;;;;;gEAGvC,KAAK,QAAQ,EAAE,wBACd,8OAAC;oEAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,QAAQ,CAAC,MAAM,KAAK,WAAW,gCACpC,KAAK,QAAQ,CAAC,MAAM,KAAK,SAAS,8BAClC,6BACA;8EACC,KAAK,QAAQ,CAAC,MAAM;;;;;;gEAGxB,KAAK,QAAQ,EAAE,wBACd,8OAAC;oEAAI,WAAU;;wEAA4C;wEACtD,KAAK,QAAQ,CAAC,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;8DAKhC,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;wDAAE,WAAW;oDAAK;;;;;;;;;;;;;;;;;;+BAhD/D,KAAK,EAAE;;;;;;;;;;oBAyDtB,CAAC,WAAW,MAAM,MAAM,GAAG,0BAC1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;;gCAAwD;gCACpD,MAAM,MAAM;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/dashboard/private/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { \n  Package, \n  Heart, \n  MessageCircle, \n  Eye, \n  Plus,\n  TrendingUp,\n  Star,\n  Calendar,\n  DollarSign,\n  Search\n} from 'lucide-react'\nimport DashboardLayout from '@/components/dashboard/DashboardLayout'\nimport { QuickStats } from '@/components/dashboard/StatsCard'\nimport ChartCard, { PlaceholderChart, ChartActions } from '@/components/dashboard/ChartCard'\nimport ActivityFeed from '@/components/dashboard/ActivityFeed'\n\ninterface PrivateDashboardStats {\n  totalListings: number\n  activeListings: number\n  totalViews: number\n  totalMessages: number\n  totalFavorites: number\n  averagePrice: number\n  recentActivity: Array<{\n    id: string\n    type: 'listing' | 'message' | 'favorite' | 'view'\n    title: string\n    description?: string\n    timestamp: string\n    metadata?: {\n      price?: number\n      status?: string\n    }\n    href?: string\n  }>\n  recentListings: Array<{\n    id: string\n    title: string\n    price: number\n    status: string\n    views: number\n    favorites: number\n    createdAt: string\n    images: string[]\n  }>\n}\n\nexport default function PrivateDashboard() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [stats, setStats] = useState<PrivateDashboardStats | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [timeRange, setTimeRange] = useState('30d')\n\n  useEffect(() => {\n    if (status === 'loading') return\n\n    if (!session) {\n      router.push('/login?message=Please sign in to access dashboard&redirect=/dashboard/private')\n      return\n    }\n\n    fetchDashboardData()\n  }, [session, status, router, timeRange])\n\n  const fetchDashboardData = async () => {\n    try {\n      const response = await fetch(`/api/dashboard/private?timeRange=${timeRange}`)\n      if (response.ok) {\n        const data = await response.json()\n        setStats(data.stats)\n      }\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (status === 'loading' || loading) {\n    return (\n      <DashboardLayout userType=\"PRIVATE\">\n        <div className=\"p-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-64 mb-8\"></div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n              {[...Array(4)].map((_, i) => (\n                <div key={i} className=\"h-32 bg-gray-200 rounded-xl\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  const quickStats = [\n    {\n      title: 'Active Listings',\n      value: stats?.activeListings || 0,\n      icon: <Package className=\"w-6 h-6\" />,\n      color: 'blue' as const,\n      change: stats?.activeListings ? {\n        value: 12,\n        type: 'increase' as const,\n        period: 'this month'\n      } : undefined\n    },\n    {\n      title: 'Total Views',\n      value: stats?.totalViews || 0,\n      icon: <Eye className=\"w-6 h-6\" />,\n      color: 'green' as const,\n      change: stats?.totalViews ? {\n        value: 8,\n        type: 'increase' as const,\n        period: 'this week'\n      } : undefined\n    },\n    {\n      title: 'Messages',\n      value: stats?.totalMessages || 0,\n      icon: <MessageCircle className=\"w-6 h-6\" />,\n      color: 'purple' as const,\n      subtitle: 'Unread: 3'\n    },\n    {\n      title: 'Favorites',\n      value: stats?.totalFavorites || 0,\n      icon: <Heart className=\"w-6 h-6\" />,\n      color: 'red' as const,\n      subtitle: 'On your listings'\n    }\n  ]\n\n  return (\n    <DashboardLayout userType=\"PRIVATE\">\n      <div className=\"p-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">\n                Welcome back, {session.user.firstName || session.user.username}! 👋\n              </h1>\n              <p className=\"text-gray-600 mt-2\">\n                Here's what's happening with your listings\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <select\n                value={timeRange}\n                onChange={(e) => setTimeRange(e.target.value)}\n                className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"7d\">Last 7 days</option>\n                <option value=\"30d\">Last 30 days</option>\n                <option value=\"90d\">Last 90 days</option>\n              </select>\n              <Link\n                href=\"/create\"\n                className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2 shadow-lg\"\n              >\n                <Plus className=\"w-4 h-4\" />\n                Create Listing\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"mb-8\">\n          <QuickStats stats={quickStats} loading={loading} />\n        </div>\n\n        {/* Charts and Activity */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          {/* Views Chart */}\n          <ChartCard\n            title=\"Listing Views\"\n            subtitle=\"Views over time\"\n            actions={<ChartActions />}\n          >\n            <PlaceholderChart \n              type=\"line\" \n              title=\"Views Trend\" \n              description=\"Track how your listings are performing\"\n            />\n          </ChartCard>\n\n          {/* Recent Activity */}\n          <ActivityFeed\n            title=\"Recent Activity\"\n            items={stats?.recentActivity || []}\n            loading={loading}\n            emptyMessage=\"No recent activity to show\"\n          />\n        </div>\n\n        {/* Recent Listings */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-100\">\n          <div className=\"p-6 border-b border-gray-100\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Your Recent Listings</h3>\n              <Link\n                href=\"/listings\"\n                className=\"text-blue-600 hover:text-blue-700 font-medium text-sm\"\n              >\n                View all listings\n              </Link>\n            </div>\n          </div>\n          <div className=\"p-6\">\n            {loading ? (\n              <div className=\"space-y-4\">\n                {[...Array(3)].map((_, i) => (\n                  <div key={i} className=\"animate-pulse flex items-center space-x-4\">\n                    <div className=\"w-16 h-16 bg-gray-200 rounded-lg\"></div>\n                    <div className=\"flex-1\">\n                      <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : stats?.recentListings && stats.recentListings.length > 0 ? (\n              <div className=\"space-y-4\">\n                {stats.recentListings.slice(0, 5).map((listing) => (\n                  <div key={listing.id} className=\"flex items-center space-x-4 p-4 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors\">\n                    <div className=\"w-16 h-16 bg-gray-200 rounded-lg overflow-hidden\">\n                      {listing.images[0] ? (\n                        <img \n                          src={listing.images[0]} \n                          alt={listing.title}\n                          className=\"w-full h-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"w-full h-full flex items-center justify-center text-gray-400\">\n                          📷\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"flex-1\">\n                      <Link \n                        href={`/listings/${listing.id}`}\n                        className=\"text-sm font-medium text-gray-900 hover:text-blue-600\"\n                      >\n                        {listing.title}\n                      </Link>\n                      <div className=\"flex items-center space-x-4 mt-1\">\n                        <span className=\"text-sm font-semibold text-green-600\">\n                          ${listing.price.toLocaleString()}\n                        </span>\n                        <span className={`text-xs px-2 py-1 rounded-full ${\n                          listing.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :\n                          listing.status === 'SOLD' ? 'bg-blue-100 text-blue-800' :\n                          'bg-gray-100 text-gray-800'\n                        }`}>\n                          {listing.status}\n                        </span>\n                        <span className=\"text-xs text-gray-500 flex items-center\">\n                          <Eye className=\"w-3 h-3 mr-1\" />\n                          {listing.views}\n                        </span>\n                        <span className=\"text-xs text-gray-500 flex items-center\">\n                          <Heart className=\"w-3 h-3 mr-1\" />\n                          {listing.favorites}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-xs text-gray-500\">\n                        {new Date(listing.createdAt).toLocaleDateString()}\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\">\n                <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No listings yet</h3>\n                <p className=\"text-gray-600 mb-4\">Start selling by creating your first listing</p>\n                <Link\n                  href=\"/create\"\n                  className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center gap-2\"\n                >\n                  <Plus className=\"w-4 h-4\" />\n                  Create Your First Listing\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Link\n            href=\"/create\"\n            className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all group\"\n          >\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform\">\n                <Plus className=\"w-6 h-6\" />\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"font-semibold\">Create Listing</h3>\n                <p className=\"text-sm opacity-90\">Sell something new</p>\n              </div>\n            </div>\n          </Link>\n\n          <Link\n            href=\"/messages\"\n            className=\"bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all group\"\n          >\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform\">\n                <MessageCircle className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"font-semibold text-gray-900\">Messages</h3>\n                <p className=\"text-sm text-gray-600\">3 unread</p>\n              </div>\n            </div>\n          </Link>\n\n          <Link\n            href=\"/favorites\"\n            className=\"bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all group\"\n          >\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform\">\n                <Heart className=\"w-6 h-6 text-red-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"font-semibold text-gray-900\">Favorites</h3>\n                <p className=\"text-sm text-gray-600\">Saved items</p>\n              </div>\n            </div>\n          </Link>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AArBA;;;;;;;;;;;AAsDe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAE1B,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAS;QAAQ;QAAQ;KAAU;IAEvC,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iCAAiC,EAAE,WAAW;YAC5E,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,8OAAC,kJAAA,CAAA,UAAe;YAAC,UAAS;sBACxB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;mCAAb;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,aAAa;QACjB;YACE,OAAO;YACP,OAAO,OAAO,kBAAkB;YAChC,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,QAAQ,OAAO,iBAAiB;gBAC9B,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV,IAAI;QACN;QACA;YACE,OAAO;YACP,OAAO,OAAO,cAAc;YAC5B,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,QAAQ,OAAO,aAAa;gBAC1B,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV,IAAI;QACN;QACA;YACE,OAAO;YACP,OAAO,OAAO,iBAAiB;YAC/B,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;YACP,UAAU;QACZ;QACA;YACE,OAAO;YACP,OAAO,OAAO,kBAAkB;YAChC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC,kJAAA,CAAA,UAAe;QAAC,UAAS;kBACxB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAAmC;4CAChC,QAAQ,IAAI,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC,QAAQ;4CAAC;;;;;;;kDAEjE,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAIpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;kDAEtB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4IAAA,CAAA,aAAU;wBAAC,OAAO;wBAAY,SAAS;;;;;;;;;;;8BAI1C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4IAAA,CAAA,UAAS;4BACR,OAAM;4BACN,UAAS;4BACT,uBAAS,8OAAC,4IAAA,CAAA,eAAY;;;;;sCAEtB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;gCACf,MAAK;gCACL,OAAM;gCACN,aAAY;;;;;;;;;;;sCAKhB,8OAAC,+IAAA,CAAA,UAAY;4BACX,OAAM;4BACN,OAAO,OAAO,kBAAkB,EAAE;4BAClC,SAAS;4BACT,cAAa;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAKL,8OAAC;4BAAI,WAAU;sCACZ,wBACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAJT;;;;;;;;;uCASZ,OAAO,kBAAkB,MAAM,cAAc,CAAC,MAAM,GAAG,kBACzD,8OAAC;gCAAI,WAAU;0CACZ,MAAM,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACrC,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,MAAM,CAAC,EAAE,iBAChB,8OAAC;oDACC,KAAK,QAAQ,MAAM,CAAC,EAAE;oDACtB,KAAK,QAAQ,KAAK;oDAClB,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DAA+D;;;;;;;;;;;0DAKlF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wDAC/B,WAAU;kEAET,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAuC;oEACnD,QAAQ,KAAK,CAAC,cAAc;;;;;;;0EAEhC,8OAAC;gEAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,MAAM,KAAK,WAAW,gCAC9B,QAAQ,MAAM,KAAK,SAAS,8BAC5B,6BACA;0EACC,QAAQ,MAAM;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEACd,QAAQ,KAAK;;;;;;;0EAEhB,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;0DAIxB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;uCA5C3C,QAAQ,EAAE;;;;;;;;;qDAmDxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAStC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;sCAKxC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAK3C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}]}