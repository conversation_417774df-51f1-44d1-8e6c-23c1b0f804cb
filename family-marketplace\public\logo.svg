<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 1020 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background curve -->
  <path d="M390 20 Q800 0 1020 60 L1020 0 L390 0 Z" fill="#1e3a8a" opacity="0.05"/>

  <!-- FM Logo Container -->
  <g transform="translate(50, 40)">
    <!-- F Letter - Modern geometric design -->
    <g fill="#1e3a8a">
      <!-- Main F structure -->
      <path d="M0 0 L0 220 L40 220 L40 130 L120 130 L120 90 L40 90 L40 40 L140 40 L140 0 Z"/>
      <!-- F top accent -->
      <rect x="0" y="0" width="140" height="40" fill="#1e3a8a"/>
      <rect x="40" y="90" width="80" height="40" fill="#1e3a8a"/>
    </g>

    <!-- M Letter - Geometric with angular design -->
    <g fill="#1e3a8a" transform="translate(180, 0)">
      <!-- Left vertical -->
      <path d="M0 0 L0 220 L40 220 L40 60 L80 160 L120 160 L160 60 L160 220 L200 220 L200 0 L150 0 L100 120 L50 0 Z"/>
      <!-- Orange accent V shape -->
      <path d="M50 0 L100 120 L150 0 L120 0 L100 60 L80 0 Z" fill="#f59e0b"/>
    </g>
  </g>

  <!-- Text -->
  <g transform="translate(420, 80)">
    <text x="0" y="60" font-family="Arial, sans-serif" font-size="72" font-weight="bold" fill="#1e3a8a" letter-spacing="3px">FAMILY</text>
    <text x="0" y="140" font-family="Arial, sans-serif" font-size="48" font-weight="500" fill="#1e3a8a" letter-spacing="8px">MARKETPLACE</text>
  </g>
</svg>
