<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 1020 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background curve -->
  <path d="M390 20 Q800 0 1020 60 L1020 0 L390 0 Z" fill="#1e3a8a" opacity="0.05"/>
  
  <!-- FM Logo Container -->
  <g transform="translate(50, 40)">
    <!-- F Letter - Clean geometric design matching the image -->
    <g fill="#1e3a8a">
      <!-- F main structure -->
      <path d="M0 0 L200 0 L160 50 L50 50 L50 90 L150 90 L150 140 L50 140 L50 220 L0 220 Z"/>
    </g>
    
    <!-- F yellow accent bar -->
    <rect x="50" y="50" width="100" height="40" fill="#fbbf24"/>
    
    <!-- M Letter - Bold geometric with V accent matching the image -->
    <g transform="translate(220, 0)">
      <!-- M main structure -->
      <g fill="#1e3a8a">
        <!-- Left vertical bar -->
        <rect x="0" y="0" width="50" height="220"/>
        <!-- Right vertical bar -->
        <rect x="150" y="0" width="50" height="220"/>
        <!-- Top connecting piece -->
        <path d="M0 0 L200 0 L200 50 L170 50 L100 170 L30 50 L0 50 Z"/>
      </g>
      
      <!-- Yellow V accent in the center -->
      <path d="M70 50 L100 120 L130 50 L115 50 L100 95 L85 50 Z" fill="#fbbf24"/>
    </g>
  </g>
  
  <!-- Text -->
  <g transform="translate(470, 80)">
    <text x="0" y="60" font-family="Arial, sans-serif" font-size="72" font-weight="bold" fill="#1e3a8a" letter-spacing="3px">FAMILY</text>
    <text x="0" y="140" font-family="Arial, sans-serif" font-size="48" font-weight="500" fill="#1e3a8a" letter-spacing="8px">MARKETPLACE</text>
  </g>
</svg>
