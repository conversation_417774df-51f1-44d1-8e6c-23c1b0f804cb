{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/listings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // For now, skip authentication and use a mock user\n    // const session = await getServerSession(authOptions)\n    //\n    // if (!session?.user?.id) {\n    //   return NextResponse.json(\n    //     { error: 'Authentication required' },\n    //     { status: 401 }\n    //   )\n    // }\n\n    const mockUserId = 'user_mock_123'\n\n    const body = await request.json()\n    const {\n      title,\n      description,\n      price,\n      condition,\n      category,\n      subcategory,\n      brand,\n      model,\n      location,\n      images,\n      aiGeneratedTitle,\n      aiGeneratedDescription,\n      aiSuggestedPrice,\n      aiConfidenceScore,\n      aiTags\n    } = body\n\n    // Validation\n    if (!title || !description || !price || !condition || !category) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      )\n    }\n\n    if (!['NEW', 'LIKE_NEW', 'GOOD', 'FAIR', 'POOR'].includes(condition)) {\n      return NextResponse.json(\n        { error: 'Invalid condition value' },\n        { status: 400 }\n      )\n    }\n\n    if (price < 0) {\n      return NextResponse.json(\n        { error: 'Price must be positive' },\n        { status: 400 }\n      )\n    }\n\n    // Create the listing\n    const listing = await prisma.listing.create({\n      data: {\n        title,\n        description,\n        price: parseFloat(price),\n        condition,\n        category,\n        subcategory: subcategory || null,\n        brand: brand || null,\n        model: model || null,\n        location: location || 'South Africa',\n        aiGeneratedTitle: aiGeneratedTitle || null,\n        aiGeneratedDescription: aiGeneratedDescription || null,\n        aiSuggestedPrice: aiSuggestedPrice ? parseFloat(aiSuggestedPrice) : null,\n        aiConfidenceScore: aiConfidenceScore ? parseFloat(aiConfidenceScore) : null,\n        aiTags: aiTags || null,\n        userId: mockUserId,\n        images: {\n          create: images?.map((image: any, index: number) => ({\n            url: image.url,\n            altText: `${title} - Image ${index + 1}`,\n            isPrimary: index === 0,\n            order: index,\n            aiAnalysis: image.aiAnalysis || null\n          })) || []\n        }\n      },\n      include: {\n        images: true,\n        user: {\n          select: {\n            id: true,\n            username: true,\n            firstName: true,\n            lastName: true,\n            userType: true,\n            vendorProfile: {\n              select: {\n                businessName: true,\n                verified: true\n              }\n            }\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: 'Listing created successfully',\n      listing\n    })\n  } catch (error) {\n    console.error('Listing creation error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '12')\n    const category = searchParams.get('category')\n    const search = searchParams.get('search')\n    const userType = searchParams.get('userType')\n    const minPrice = searchParams.get('minPrice')\n    const maxPrice = searchParams.get('maxPrice')\n    const condition = searchParams.get('condition')\n\n    const skip = (page - 1) * limit\n\n    // Build where clause\n    const where: any = {\n      status: 'ACTIVE'\n    }\n\n    if (category) {\n      where.category = category\n    }\n\n    if (search) {\n      where.OR = [\n        { title: { contains: search, mode: 'insensitive' } },\n        { description: { contains: search, mode: 'insensitive' } },\n        { brand: { contains: search, mode: 'insensitive' } },\n        { model: { contains: search, mode: 'insensitive' } }\n      ]\n    }\n\n    if (userType) {\n      where.user = {\n        userType: userType\n      }\n    }\n\n    if (minPrice || maxPrice) {\n      where.price = {}\n      if (minPrice) where.price.gte = parseFloat(minPrice)\n      if (maxPrice) where.price.lte = parseFloat(maxPrice)\n    }\n\n    if (condition) {\n      where.condition = condition\n    }\n\n    // Get listings with pagination\n    const [listings, total] = await Promise.all([\n      prisma.listing.findMany({\n        where,\n        include: {\n          images: {\n            orderBy: { order: 'asc' }\n          },\n          user: {\n            select: {\n              id: true,\n              username: true,\n              firstName: true,\n              lastName: true,\n              userType: true,\n              vendorProfile: {\n                select: {\n                  businessName: true,\n                  verified: true\n                }\n              }\n            }\n          }\n        },\n        orderBy: {\n          createdAt: 'desc'\n        },\n        skip,\n        take: limit\n      }),\n      prisma.listing.count({ where })\n    ])\n\n    return NextResponse.json({\n      success: true,\n      listings,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n        hasMore: skip + limit < total\n      }\n    })\n  } catch (error) {\n    console.error('Listings fetch error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAGA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,mDAAmD;QACnD,sDAAsD;QACtD,EAAE;QACF,4BAA4B;QAC5B,8BAA8B;QAC9B,4CAA4C;QAC5C,sBAAsB;QACtB,MAAM;QACN,IAAI;QAEJ,MAAM,aAAa;QAEnB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,WAAW,EACX,KAAK,EACL,SAAS,EACT,QAAQ,EACR,WAAW,EACX,KAAK,EACL,KAAK,EACL,QAAQ,EACR,MAAM,EACN,gBAAgB,EAChB,sBAAsB,EACtB,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,EACP,GAAG;QAEJ,aAAa;QACb,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC;YAAC;YAAO;YAAY;YAAQ;YAAQ;SAAO,CAAC,QAAQ,CAAC,YAAY;YACpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ,GAAG;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA;gBACA,OAAO,WAAW;gBAClB;gBACA;gBACA,aAAa,eAAe;gBAC5B,OAAO,SAAS;gBAChB,OAAO,SAAS;gBAChB,UAAU,YAAY;gBACtB,kBAAkB,oBAAoB;gBACtC,wBAAwB,0BAA0B;gBAClD,kBAAkB,mBAAmB,WAAW,oBAAoB;gBACpE,mBAAmB,oBAAoB,WAAW,qBAAqB;gBACvE,QAAQ,UAAU;gBAClB,QAAQ;gBACR,QAAQ;oBACN,QAAQ,QAAQ,IAAI,CAAC,OAAY,QAAkB,CAAC;4BAClD,KAAK,MAAM,GAAG;4BACd,SAAS,GAAG,MAAM,SAAS,EAAE,QAAQ,GAAG;4BACxC,WAAW,UAAU;4BACrB,OAAO;4BACP,YAAY,MAAM,UAAU,IAAI;wBAClC,CAAC,MAAM,EAAE;gBACX;YACF;YACA,SAAS;gBACP,QAAQ;gBACR,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,eAAe;4BACb,QAAQ;gCACN,cAAc;gCACd,UAAU;4BACZ;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,qBAAqB;QACrB,MAAM,QAAa;YACjB,QAAQ;QACV;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACzD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aACpD;QACH;QAEA,IAAI,UAAU;YACZ,MAAM,IAAI,GAAG;gBACX,UAAU;YACZ;QACF;QAEA,IAAI,YAAY,UAAU;YACxB,MAAM,KAAK,GAAG,CAAC;YACf,IAAI,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,WAAW;YAC3C,IAAI,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,WAAW;QAC7C;QAEA,IAAI,WAAW;YACb,MAAM,SAAS,GAAG;QACpB;QAEA,+BAA+B;QAC/B,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB;gBACA,SAAS;oBACP,QAAQ;wBACN,SAAS;4BAAE,OAAO;wBAAM;oBAC1B;oBACA,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,UAAU;4BACV,WAAW;4BACX,UAAU;4BACV,UAAU;4BACV,eAAe;gCACb,QAAQ;oCACN,cAAc;oCACd,UAAU;gCACZ;4BACF;wBACF;oBACF;gBACF;gBACA,SAAS;oBACP,WAAW;gBACb;gBACA;gBACA,MAAM;YACR;YACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC9B;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;gBACzB,SAAS,OAAO,QAAQ;YAC1B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}