module.exports = {

"[project]/.next-internal/server/app/api/ai/suggest-price/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions),
    "createUser": (()=>createUser),
    "getUserByEmail": (()=>getUserByEmail),
    "getUserByUsername": (()=>getUserByUsername)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                    where: {
                        email: credentials.email
                    },
                    include: {
                        vendorProfile: true
                    }
                });
                if (!user) {
                    return null;
                }
                const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                return {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    userType: user.userType,
                    isVerified: user.isVerified,
                    avatar: user.avatar,
                    vendorProfile: user.vendorProfile
                };
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
                token.username = user.username;
                token.userType = user.userType;
                token.isVerified = user.isVerified;
                token.vendorProfile = user.vendorProfile;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.id;
                session.user.username = token.username;
                session.user.userType = token.userType;
                session.user.isVerified = token.isVerified;
                session.user.vendorProfile = token.vendorProfile;
            }
            return session;
        }
    },
    pages: {
        signIn: '/login',
        signUp: '/register'
    }
};
async function createUser(userData) {
    const hashedPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(userData.password, 12);
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.create({
        data: {
            email: userData.email,
            username: userData.username,
            password: hashedPassword,
            firstName: userData.firstName,
            lastName: userData.lastName,
            userType: userData.userType,
            vendorProfile: userData.userType === 'VENDOR' && userData.vendorData ? {
                create: {
                    businessName: userData.vendorData.businessName,
                    description: userData.vendorData.description,
                    website: userData.vendorData.website
                }
            } : undefined
        },
        include: {
            vendorProfile: true
        }
    });
    return user;
}
async function getUserByEmail(email) {
    return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            email
        },
        include: {
            vendorProfile: true
        }
    });
}
async function getUserByUsername(username) {
    return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            username
        },
        include: {
            vendorProfile: true
        }
    });
}
}}),
"[project]/src/lib/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIService": (()=>AIService),
    "openai": (()=>openai)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
if (!process.env.OPENAI_API_KEY) {
    throw new Error('OPENAI_API_KEY is not set in environment variables');
}
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
class AIService {
    /**
   * Analyze an image to extract product information
   */ static async analyzeImage(imageUrl) {
        // If it's a localhost URL, convert to base64
        if (imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
            return this.analyzeImageFromLocalhost(imageUrl);
        }
        try {
            const response = await openai.chat.completions.create({
                model: "gpt-4o",
                messages: [
                    {
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `Analyze this product image for a South African marketplace listing. Extract information in JSON format:
                {
                  "category": "Main category: Electronics, Furniture, Clothing, Books, Sports & Outdoors, Home & Garden, Toys & Games, Automotive, Health & Beauty, or Other",
                  "subcategory": "Specific subcategory if applicable",
                  "title": "Descriptive title suitable for South African buyers",
                  "description": "Detailed description highlighting key features and condition",
                  "condition": "Realistic condition: NEW, LIKE_NEW, GOOD, FAIR, or POOR",
                  "brand": "Brand name if visible/identifiable",
                  "model": "Model name/number if visible",
                  "tags": ["relevant", "search", "keywords", "for", "SA", "market"],
                  "confidence": 0.95
                }

                Guidelines:
                - Focus on details visible in the image
                - Consider South African market preferences
                - Be realistic about condition assessment
                - Include relevant search terms for local buyers
                - If uncertain about any field, use null`
                            },
                            {
                                type: "image_url",
                                image_url: {
                                    url: imageUrl
                                }
                            }
                        ]
                    }
                ],
                max_tokens: 1000,
                temperature: 0.3
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                throw new Error('No response from OpenAI');
            }
            // Parse JSON response (handle markdown code blocks)
            const cleanContent = this.extractJsonFromResponse(content);
            const result = JSON.parse(cleanContent);
            // Log analytics
            await this.logAnalytics('image_analysis', {
                imageUrl
            }, result, result.confidence, 'gpt-4-vision-preview');
            return result;
        } catch (error) {
            console.error('Error analyzing image:', error);
            // Provide more specific error messages
            if (error instanceof Error) {
                if (error.message.includes('Invalid image')) {
                    throw new Error('The image format is not supported or the image is corrupted');
                }
                if (error.message.includes('rate limit') || error.message.includes('429') || error.message.includes('quota')) {
                    throw new Error('🚫 AI service quota exceeded. Please check your OpenAI billing or try again later.');
                }
                if (error.message.includes('API key')) {
                    throw new Error('AI service configuration error');
                }
            }
            throw new Error('Failed to analyze image. Please try again.');
        }
    }
    /**
   * Analyze image from localhost by converting to base64
   */ static async analyzeImageFromLocalhost(imageUrl) {
        try {
            // Fetch the image from localhost
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error('Failed to fetch image from localhost');
            }
            const buffer = await response.arrayBuffer();
            const base64 = Buffer.from(buffer).toString('base64');
            const mimeType = response.headers.get('content-type') || 'image/jpeg';
            const base64Url = `data:${mimeType};base64,${base64}`;
            const openaiResponse = await openai.chat.completions.create({
                model: "gpt-4o",
                messages: [
                    {
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `Analyze this product image for a South African marketplace listing. Extract information in JSON format:
                {
                  "category": "Main category: Electronics, Furniture, Clothing, Books, Sports & Outdoors, Home & Garden, Toys & Games, Automotive, Health & Beauty, or Other",
                  "subcategory": "Specific subcategory if applicable",
                  "title": "Descriptive title suitable for South African buyers",
                  "description": "Detailed description highlighting key features and condition",
                  "condition": "Realistic condition: NEW, LIKE_NEW, GOOD, FAIR, or POOR",
                  "brand": "Brand name if visible/identifiable",
                  "model": "Model name/number if visible",
                  "tags": ["relevant", "search", "keywords", "for", "SA", "market"],
                  "confidence": 0.95
                }

                Guidelines:
                - Focus on details visible in the image
                - Consider South African market preferences
                - Be realistic about condition assessment
                - Include relevant search terms for local buyers
                - If uncertain about any field, use null`
                            },
                            {
                                type: "image_url",
                                image_url: {
                                    url: base64Url,
                                    detail: "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens: 1000,
                temperature: 0.3
            });
            const content = openaiResponse.choices[0]?.message?.content;
            if (!content) {
                throw new Error('No response from OpenAI');
            }
            // Parse JSON response (handle markdown code blocks)
            const cleanContent = this.extractJsonFromResponse(content);
            const result = JSON.parse(cleanContent);
            // Log analytics
            await this.logAnalytics('image_analysis', {
                imageUrl
            }, result, result.confidence, 'gpt-4o');
            return result;
        } catch (error) {
            console.error('Error analyzing localhost image:', error);
            // Provide more specific error messages
            if (error instanceof Error) {
                if (error.message.includes('Invalid image')) {
                    throw new Error('The image format is not supported or the image is corrupted');
                }
                if (error.message.includes('rate limit') || error.message.includes('429') || error.message.includes('quota')) {
                    throw new Error('🚫 AI service quota exceeded. Please check your OpenAI billing or try again later.');
                }
                if (error.message.includes('API key')) {
                    throw new Error('AI service configuration error');
                }
            }
            throw new Error('Failed to analyze image. Please try again.');
        }
    }
    /**
   * Generate price suggestions based on product details and market data
   */ static async suggestPrice(productInfo) {
        try {
            const response = await openai.chat.completions.create({
                model: "gpt-4",
                messages: [
                    {
                        role: "system",
                        content: "You are a South African marketplace pricing expert. Suggest realistic prices in South African Rand (ZAR) based on local market conditions, brand reputation, item condition, and economic factors specific to South Africa."
                    },
                    {
                        role: "user",
                        content: `Suggest a realistic price for this product in the South African market (ZAR):

            Product Details:
            - Category: ${productInfo.category}
            - Subcategory: ${productInfo.subcategory || 'N/A'}
            - Brand: ${productInfo.brand || 'N/A'}
            - Model: ${productInfo.model || 'N/A'}
            - Condition: ${productInfo.condition}
            - Description: ${productInfo.description}

            Consider South African factors:
            - Local market demand and supply
            - Brand availability and reputation in SA
            - Economic conditions and purchasing power
            - Import costs and local alternatives
            - Typical depreciation rates for used items

            Return JSON format (prices in ZAR):
            {
              "suggestedPrice": 1500,
              "priceRange": {
                "min": 1200,
                "max": 1800
              },
              "confidence": 0.85,
              "reasoning": "Brief explanation considering SA market factors"
            }`
                    }
                ],
                max_tokens: 500,
                temperature: 0.3
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                throw new Error('No response from OpenAI');
            }
            const cleanContent = this.extractJsonFromResponse(content);
            const result = JSON.parse(cleanContent);
            // Log analytics
            await this.logAnalytics('price_suggestion', productInfo, result, result.confidence, 'gpt-4');
            return result;
        } catch (error) {
            console.error('Error suggesting price:', error);
            throw new Error('Failed to suggest price');
        }
    }
    /**
   * Generate improved product descriptions
   */ static async enhanceDescription(originalDescription, productInfo) {
        try {
            const response = await openai.chat.completions.create({
                model: "gpt-4",
                messages: [
                    {
                        role: "system",
                        content: "You are a copywriting expert specializing in marketplace listings. Create compelling, accurate product descriptions that highlight key features and benefits while maintaining honesty about condition and details."
                    },
                    {
                        role: "user",
                        content: `Enhance this product description to make it more appealing and informative:

            Original: "${originalDescription}"
            
            Product Info: ${JSON.stringify(productInfo)}
            
            Guidelines:
            - Keep it honest and accurate
            - Highlight key features and benefits
            - Use engaging but professional language
            - Include relevant keywords for searchability
            - Mention condition appropriately
            - Keep it concise but informative (2-3 paragraphs max)
            
            Return only the enhanced description, no JSON or extra formatting.`
                    }
                ],
                max_tokens: 400,
                temperature: 0.7
            });
            const enhancedDescription = response.choices[0]?.message?.content?.trim();
            if (!enhancedDescription) {
                throw new Error('No response from OpenAI');
            }
            // Log analytics
            await this.logAnalytics('description_enhancement', {
                originalDescription,
                productInfo
            }, {
                enhancedDescription
            }, 0.9, 'gpt-4');
            return enhancedDescription;
        } catch (error) {
            console.error('Error enhancing description:', error);
            return originalDescription // Fallback to original
            ;
        }
    }
    /**
   * Generate auto-responses for common buyer questions
   */ static async generateAutoResponse(question, listingInfo) {
        try {
            const response = await openai.chat.completions.create({
                model: "gpt-4",
                messages: [
                    {
                        role: "system",
                        content: "You are a helpful assistant for a marketplace seller. Generate polite, informative responses to buyer questions based on the listing information. Be helpful but direct sellers to contact for specific details not in the listing."
                    },
                    {
                        role: "user",
                        content: `Generate a response to this buyer question:

            Question: "${question}"
            
            Listing Info: ${JSON.stringify(listingInfo)}
            
            Guidelines:
            - Be polite and helpful
            - Use information from the listing when available
            - For specific details not in listing, suggest contacting the seller
            - Keep responses concise but friendly
            - Don't make up information not in the listing
            
            Return only the response message.`
                    }
                ],
                max_tokens: 200,
                temperature: 0.7
            });
            const autoResponse = response.choices[0]?.message?.content?.trim();
            if (!autoResponse) {
                throw new Error('No response from OpenAI');
            }
            // Log analytics
            await this.logAnalytics('auto_response', {
                question,
                listingInfo
            }, {
                autoResponse
            }, 0.8, 'gpt-4');
            return autoResponse;
        } catch (error) {
            console.error('Error generating auto response:', error);
            return "Thank you for your question! Please feel free to contact the seller directly for more specific details about this item.";
        }
    }
    /**
   * Extract JSON from markdown-wrapped responses
   */ static extractJsonFromResponse(content) {
        // Remove markdown code blocks if present
        const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
        if (jsonMatch) {
            return jsonMatch[1].trim();
        }
        // If no markdown blocks, try to find JSON object
        const jsonObjectMatch = content.match(/\{[\s\S]*\}/);
        if (jsonObjectMatch) {
            return jsonObjectMatch[0].trim();
        }
        // Return original content if no JSON found
        return content.trim();
    }
    /**
   * Log AI analytics for tracking and optimization
   */ static async logAnalytics(type, input, output, confidence, model) {
        try {
            const { prisma } = await __turbopack_context__.r("[project]/src/lib/prisma.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            await prisma.aIAnalytics.create({
                data: {
                    type,
                    input,
                    output,
                    confidence,
                    model,
                    cost: this.estimateCost(type, model) // Rough cost estimation
                }
            });
        } catch (error) {
            console.error('Error logging analytics:', error);
        // Don't throw - analytics logging shouldn't break the main functionality
        }
    }
    /**
   * Estimate API costs for budgeting
   */ static estimateCost(type, model) {
        // Rough cost estimates based on OpenAI pricing (as of 2024)
        const costs = {
            'gpt-4': 0.03,
            'gpt-4-vision-preview': 0.01,
            'gpt-3.5-turbo': 0.002 // per 1K tokens
        };
        const baseCost = costs[model] || 0.01;
        // Multiply by estimated token usage
        const tokenMultipliers = {
            'image_analysis': 800,
            'price_suggestion': 400,
            'description_enhancement': 300,
            'auto_response': 150
        };
        const tokens = tokenMultipliers[type] || 200;
        return baseCost * tokens / 1000;
    }
}
}}),
"[project]/src/app/api/ai/suggest-price/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openai.ts [app-route] (ecmascript)");
;
;
;
;
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user?.id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required'
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const { productInfo } = body;
        if (!productInfo || !productInfo.category || !productInfo.description) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Product information (category and description) is required'
            }, {
                status: 400
            });
        }
        // Generate price suggestion with AI
        const priceSuggestion = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIService"].suggestPrice(productInfo);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            priceSuggestion
        });
    } catch (error) {
        console.error('AI price suggestion error:', error);
        // Return a more specific error message
        if (error instanceof Error) {
            if (error.message.includes('API key')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'AI service configuration error'
                }, {
                    status: 500
                });
            }
            if (error.message.includes('quota') || error.message.includes('rate limit')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'AI service temporarily unavailable. Please try again later.'
                }, {
                    status: 429
                });
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate price suggestion. Please try again.'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9e379c94._.js.map