{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          },\n          include: {\n            vendorProfile: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          userType: user.userType,\n          isVerified: user.isVerified,\n          avatar: user.avatar,\n          vendorProfile: user.vendorProfile\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n        token.username = user.username\n        token.userType = user.userType\n        token.isVerified = user.isVerified\n        token.vendorProfile = user.vendorProfile\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n        session.user.username = token.username as string\n        session.user.userType = token.userType as string\n        session.user.isVerified = token.isVerified as boolean\n        session.user.vendorProfile = token.vendorProfile as any\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/login',\n    signUp: '/register'\n  }\n}\n\n// Helper functions for user management\nexport async function createUser(userData: {\n  email: string\n  username: string\n  password: string\n  firstName?: string\n  lastName?: string\n  userType: 'PRIVATE' | 'VENDOR'\n  vendorData?: {\n    businessName: string\n    description?: string\n    website?: string\n  }\n}) {\n  const hashedPassword = await bcrypt.hash(userData.password, 12)\n\n  const user = await prisma.user.create({\n    data: {\n      email: userData.email,\n      username: userData.username,\n      password: hashedPassword,\n      firstName: userData.firstName,\n      lastName: userData.lastName,\n      userType: userData.userType,\n      vendorProfile: userData.userType === 'VENDOR' && userData.vendorData ? {\n        create: {\n          businessName: userData.vendorData.businessName,\n          description: userData.vendorData.description,\n          website: userData.vendorData.website\n        }\n      } : undefined\n    },\n    include: {\n      vendorProfile: true\n    }\n  })\n\n  return user\n}\n\nexport async function getUserByEmail(email: string) {\n  return await prisma.user.findUnique({\n    where: { email },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n\nexport async function getUserByUsername(username: string) {\n  return await prisma.user.findUnique({\n    where: { username },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;oBACA,SAAS;wBACP,eAAe;oBACjB;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,aAAa;gBACnC;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,aAAa,GAAG,KAAK,aAAa;YAC1C;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;YAClD;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,eAAe,WAAW,QAYhC;IACC,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;IAE5D,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM;YACJ,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;YAC3B,UAAU;YACV,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;YAC3B,eAAe,SAAS,QAAQ,KAAK,YAAY,SAAS,UAAU,GAAG;gBACrE,QAAQ;oBACN,cAAc,SAAS,UAAU,CAAC,YAAY;oBAC9C,aAAa,SAAS,UAAU,CAAC,WAAW;oBAC5C,SAAS,SAAS,UAAU,CAAC,OAAO;gBACtC;YACF,IAAI;QACN;QACA,SAAS;YACP,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,KAAa;IAChD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAM;QACf,SAAS;YACP,eAAe;QACjB;IACF;AACF;AAEO,eAAe,kBAAkB,QAAgB;IACtD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,eAAe;QACjB;IACF;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/dashboard/private/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n    const timeRange = searchParams.get('timeRange') || '30d'\n    \n    // Calculate date range\n    const now = new Date()\n    const daysAgo = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90\n    const startDate = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000))\n\n    // Get user's listings\n    const listings = await prisma.listing.findMany({\n      where: {\n        userId: session.user.id\n      },\n      include: {\n        images: true,\n        favorites: true,\n        _count: {\n          select: {\n            favorites: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    // Get user's messages\n    const messages = await prisma.message.findMany({\n      where: {\n        OR: [\n          { senderId: session.user.id },\n          { receiverId: session.user.id }\n        ]\n      }\n    })\n\n    // Calculate stats\n    const totalListings = listings.length\n    const activeListings = listings.filter(l => l.status === 'ACTIVE').length\n    const totalViews = listings.reduce((sum, listing) => sum + (listing.views || 0), 0)\n    const totalMessages = messages.length\n    const totalFavorites = listings.reduce((sum, listing) => sum + listing._count.favorites, 0)\n    const averagePrice = listings.length > 0 \n      ? listings.reduce((sum, listing) => sum + listing.price, 0) / listings.length \n      : 0\n\n    // Generate recent activity (mock data for now)\n    const recentActivity = [\n      {\n        id: '1',\n        type: 'listing' as const,\n        title: 'New listing created',\n        description: 'Your listing \"Vintage Camera\" is now live',\n        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        metadata: {\n          price: 250,\n          status: 'ACTIVE'\n        },\n        href: '/listings/1'\n      },\n      {\n        id: '2',\n        type: 'message' as const,\n        title: 'New message received',\n        description: 'Someone is interested in your iPhone',\n        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n        href: '/messages'\n      },\n      {\n        id: '3',\n        type: 'favorite' as const,\n        title: 'Item favorited',\n        description: 'Your laptop was added to someone\\'s favorites',\n        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()\n      },\n      {\n        id: '4',\n        type: 'view' as const,\n        title: 'Listing viewed',\n        description: 'Your bike listing got 5 new views',\n        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()\n      }\n    ]\n\n    // Format recent listings\n    const recentListings = listings.slice(0, 10).map(listing => ({\n      id: listing.id,\n      title: listing.title,\n      price: listing.price,\n      status: listing.status,\n      views: listing.views || 0,\n      favorites: listing._count.favorites,\n      createdAt: listing.createdAt.toISOString(),\n      images: listing.images.map(img => img.url)\n    }))\n\n    const stats = {\n      totalListings,\n      activeListings,\n      totalViews,\n      totalMessages,\n      totalFavorites,\n      averagePrice,\n      recentActivity,\n      recentListings\n    }\n\n    return NextResponse.json({\n      success: true,\n      stats\n    })\n\n  } catch (error) {\n    console.error('Dashboard API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch dashboard data' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,uBAAuB;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,cAAc,OAAO,IAAI,cAAc,QAAQ,KAAK;QACpE,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAM,UAAU,KAAK,KAAK,KAAK;QAErE,sBAAsB;QACtB,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;YACA,SAAS;gBACP,QAAQ;gBACR,WAAW;gBACX,QAAQ;oBACN,QAAQ;wBACN,WAAW;oBACb;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,sBAAsB;QACtB,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,IAAI;oBACF;wBAAE,UAAU,QAAQ,IAAI,CAAC,EAAE;oBAAC;oBAC5B;wBAAE,YAAY,QAAQ,IAAI,CAAC,EAAE;oBAAC;iBAC/B;YACH;QACF;QAEA,kBAAkB;QAClB,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QACzE,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG;QACjF,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,CAAC,SAAS,EAAE;QACzF,MAAM,eAAe,SAAS,MAAM,GAAG,IACnC,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,KAAK,EAAE,KAAK,SAAS,MAAM,GAC3E;QAEJ,+CAA+C;QAC/C,MAAM,iBAAiB;YACrB;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBAChE,UAAU;oBACR,OAAO;oBACP,QAAQ;gBACV;gBACA,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBAChE,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YAClE;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YAClE;SACD;QAED,yBAAyB;QACzB,MAAM,iBAAiB,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3D,IAAI,QAAQ,EAAE;gBACd,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,KAAK;gBACpB,QAAQ,QAAQ,MAAM;gBACtB,OAAO,QAAQ,KAAK,IAAI;gBACxB,WAAW,QAAQ,MAAM,CAAC,SAAS;gBACnC,WAAW,QAAQ,SAAS,CAAC,WAAW;gBACxC,QAAQ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG;YAC3C,CAAC;QAED,MAAM,QAAQ;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}