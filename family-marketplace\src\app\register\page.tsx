'use client'

import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import RegistrationForm from '@/components/RegistrationForm'
import Link from 'next/link'

export default function RegisterPage() {
  const searchParams = useSearchParams()
  const type = searchParams.get('type')

  // Get the user type from URL parameter
  const getUserTypeFromUrl = () => {
    if (type === 'vendor') return 'VENDOR'
    if (type === 'private') return 'PRIVATE'
    return null
  }

  const urlUserType = getUserTypeFromUrl()

  return (
    <div className="bg-gradient-to-br from-blue-50 to-purple-50 py-12">

      {/* Page Title with User Type Indicator */}
      {urlUserType && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
          <div className="text-center">
            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
              urlUserType === 'VENDOR'
                ? 'bg-purple-100 text-purple-800'
                : 'bg-blue-100 text-blue-800'
            }`}>
              <span className="mr-2">
                {urlUserType === 'VENDOR' ? '🏪' : '👤'}
              </span>
              Creating {urlUserType === 'VENDOR' ? 'Vendor' : 'Private Seller'} Account
            </div>
          </div>
        </div>
      )}

      {/* Registration Form */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <RegistrationForm />
      </div>

      {/* Benefits Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Private Seller Benefits */}
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">👤</span>
              <h3 className="text-xl font-semibold">Private Seller Benefits</h3>
            </div>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Free listing creation with AI assistance
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Smart photo analysis and categorization
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Intelligent price suggestions
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Simple messaging system
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Perfect for decluttering and one-time sales
              </li>
            </ul>
          </div>

          {/* Vendor Benefits */}
          <div className="bg-white rounded-lg p-6 shadow-lg border-2 border-purple-200">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">🏪</span>
              <h3 className="text-xl font-semibold">Vendor Benefits</h3>
              <span className="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                Business
              </span>
            </div>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">✓</span>
                All private seller features included
              </li>
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">✓</span>
                Bulk upload and inventory management
              </li>
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">✓</span>
                Advanced analytics and insights
              </li>
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">✓</span>
                Custom business storefront
              </li>
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">✓</span>
                Priority customer support
              </li>
              <li className="flex items-center">
                <span className="text-purple-500 mr-2">✓</span>
                Multi-location inventory tracking
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
