import Link from 'next/link'
import { 
  Upload, 
  Wand2, 
  DollarSign, 
  Search, 
  MessageCircle, 
  Shield,
  Camera,
  Brain,
  Zap,
  Users,
  Star,
  CheckCircle
} from 'lucide-react'

export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              How Family Marketplace Works
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Experience the future of online selling with our AI-powered platform designed for South African families and businesses
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/register?type=private"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Start Selling Today
              </Link>
              <Link
                href="/browse"
                className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
              >
                Browse Items
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Steps */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Selling Made Simple with AI
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our revolutionary AI technology transforms how you create listings, making it faster and more effective than ever before
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {/* Step 1 */}
          <div className="text-center">
            <div className="bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
              <Camera className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">1. Upload Photos</h3>
            <p className="text-gray-600 mb-6">
              Simply take photos of your item with your phone or camera. Our AI works with any quality image.
            </p>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="bg-gray-100 h-32 rounded-lg flex items-center justify-center mb-3">
                <Upload className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-sm text-gray-500">Drag & drop or click to upload</p>
            </div>
          </div>

          {/* Step 2 */}
          <div className="text-center">
            <div className="bg-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
              <Brain className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">2. AI Analysis</h3>
            <p className="text-gray-600 mb-6">
              Our advanced AI instantly analyzes your photos, identifies the item, and suggests categories, titles, and descriptions.
            </p>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="flex items-center justify-center mb-3">
                <Wand2 className="w-6 h-6 text-purple-600 mr-2" />
                <span className="text-purple-600 font-medium">Analyzing...</span>
              </div>
              <div className="space-y-2 text-left">
                <div className="flex justify-between text-sm">
                  <span>Category:</span>
                  <span className="text-green-600">✓ Bakkies & Cars</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Condition:</span>
                  <span className="text-green-600">✓ Good</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Brand:</span>
                  <span className="text-green-600">✓ Toyota</span>
                </div>
              </div>
            </div>
          </div>

          {/* Step 3 */}
          <div className="text-center">
            <div className="bg-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">3. Instant Listing</h3>
            <p className="text-gray-600 mb-6">
              Review the AI suggestions, make any adjustments, and publish your listing in under 2 minutes.
            </p>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="text-left space-y-2">
                <div className="font-medium text-gray-900">Toyota Hilux 2018</div>
                <div className="text-2xl font-bold text-green-600">R 350,000</div>
                <div className="text-sm text-gray-500">AI suggested price range: R 320k - R 380k</div>
                <button className="w-full bg-green-600 text-white py-2 rounded-lg text-sm font-medium">
                  Publish Listing
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* AI Features */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              🤖 Powered by Advanced AI
            </h2>
            <p className="text-xl text-gray-600">
              Our AI technology is specifically trained for the South African market
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Brain className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Smart Recognition</h4>
              <p className="text-sm text-gray-600">
                Identifies products, brands, and conditions from photos with 95% accuracy
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Price Intelligence</h4>
              <p className="text-sm text-gray-600">
                Suggests optimal prices based on SA market data and current trends
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Wand2 className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Auto-Description</h4>
              <p className="text-sm text-gray-600">
                Generates compelling descriptions using local terminology and keywords
              </p>
            </div>

            <div className="text-center">
              <div className="bg-orange-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Search className="w-6 h-6 text-orange-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">SEO Optimization</h4>
              <p className="text-sm text-gray-600">
                Optimizes listings for better visibility and faster sales
              </p>
            </div>
          </div>
        </div>

        {/* For Buyers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">For Buyers</h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="bg-blue-100 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Search className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Smart Search</h4>
                  <p className="text-gray-600">
                    Find exactly what you're looking for with AI-powered search and visual similarity matching
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-green-100 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Shield className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Verified Listings</h4>
                  <p className="text-gray-600">
                    AI-verified product information and seller verification for safe transactions
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-purple-100 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <MessageCircle className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Easy Communication</h4>
                  <p className="text-gray-600">
                    Built-in messaging system with AI-powered response suggestions
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">For Sellers</h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="bg-orange-100 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Zap className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Lightning Fast</h4>
                  <p className="text-gray-600">
                    Create professional listings in under 2 minutes with AI assistance
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-red-100 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <DollarSign className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Maximize Profits</h4>
                  <p className="text-gray-600">
                    AI-suggested pricing helps you get the best value for your items
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-indigo-100 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Users className="w-5 h-5 text-indigo-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Reach More Buyers</h4>
                  <p className="text-gray-600">
                    Optimized listings reach the right audience faster
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* User Types */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Built for Everyone
            </h2>
            <p className="text-xl text-gray-600">
              Whether you're a family selling household items or a business expanding online
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mr-4">
                  <span className="text-2xl">👤</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">Private Sellers</h3>
                  <p className="text-gray-600">Families & Individuals</p>
                </div>
              </div>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>Free to list and sell</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>AI-powered listing creation</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>Safe & secure transactions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>Community support</span>
                </li>
              </ul>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mr-4">
                  <span className="text-2xl">🏪</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">Business Vendors</h3>
                  <p className="text-gray-600">SMEs & Enterprises</p>
                </div>
              </div>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>Advanced analytics dashboard</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>Bulk listing management</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>Verified business badge</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <span>Priority customer support</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Success Stories */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Success Stories
          </h2>
          <p className="text-xl text-gray-600 mb-12">
            See how our AI-powered platform is helping South Africans sell better
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  S
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Sarah M.</h4>
                  <p className="text-gray-600 text-sm">Cape Town</p>
                </div>
              </div>
              <div className="flex items-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 text-sm">
                "Sold my old furniture in just 3 days! The AI made creating listings so easy, and the price suggestion was spot on."
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  T
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Thabo's Electronics</h4>
                  <p className="text-gray-600 text-sm">Johannesburg</p>
                </div>
              </div>
              <div className="flex items-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 text-sm">
                "As a small business, this platform has been a game-changer. Sales increased by 40% since we started using the AI features."
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  M
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Maria K.</h4>
                  <p className="text-gray-600 text-sm">Durban</p>
                </div>
              </div>
              <div className="flex items-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 text-sm">
                "The AI understood exactly what I was selling from just a photo. Saved me hours of writing descriptions!"
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-700 rounded-2xl p-8 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Experience AI-Powered Selling?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of South Africans who are already selling smarter with our AI technology
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register?type=private"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
            >
              Start Selling for Free
            </Link>
            <Link
              href="/register?type=vendor"
              className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
            >
              Join as Business
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
