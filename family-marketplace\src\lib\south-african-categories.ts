// South African localized categories and terminology for the marketplace

export interface CategoryWithSubcategories {
  name: string
  icon: string
  subcategories: string[]
}

export const SOUTH_AFRICAN_CATEGORIES: CategoryWithSubcategories[] = [
  {
    name: 'Bakkies & Cars',
    icon: '🚗',
    subcategories: [
      '<PERSON><PERSON><PERSON><PERSON> (Pickup Trucks)',
      'Sedans',
      'Hatchbacks', 
      'SUVs',
      'Combis (Minivans)',
      'Motorcycles',
      'Scooters',
      'Car Parts & Accessories',
      'Tyres & Wheels',
      'Car Audio & Electronics'
    ]
  },
  {
    name: 'Electronics',
    icon: '📱',
    subcategories: [
      'Cell Phones',
      'Laptops & Computers',
      'TVs & Audio',
      'Gaming Consoles',
      'Cameras',
      'Tablets',
      'Smart Watches',
      'Headphones',
      'Chargers & Cables',
      'Other Electronics'
    ]
  },
  {
    name: 'Furniture',
    icon: '🪑',
    subcategories: [
      'Lounge Suites',
      'Bedroom Sets',
      'Dining Room Sets',
      'Office Furniture',
      'Outdoor Furniture',
      'Wardrobes',
      'Beds & Mattresses',
      'Tables & Chairs',
      'Storage Solutions',
      'Antiques'
    ]
  },
  {
    name: 'Home & Garden',
    icon: '🏠',
    subcategories: [
      'Garden Tools',
      'Braai Equipment',
      'Pool Equipment',
      'Security Systems',
      'Solar Equipment',
      'Kitchen Appliances',
      'Cleaning Equipment',
      'Home Decor',
      'Lighting',
      'Plumbing & Electrical'
    ]
  },
  {
    name: 'Sport & Outdoor',
    icon: '⚽',
    subcategories: [
      'Rugby Equipment',
      'Cricket Equipment',
      'Soccer Equipment',
      'Golf Equipment',
      'Fishing Gear',
      'Camping & Hiking',
      'Bicycles',
      'Gym Equipment',
      'Water Sports',
      'Hunting Equipment'
    ]
  },
  {
    name: 'Clothing',
    icon: '👕',
    subcategories: [
      'Mens Clothing',
      'Womens Clothing',
      'Kids Clothing',
      'Shoes',
      'Accessories',
      'Traditional Wear',
      'Work Wear',
      'Sports Wear',
      'Formal Wear',
      'Vintage Clothing'
    ]
  },
  {
    name: 'Health & Beauty',
    icon: '💄',
    subcategories: [
      'Skincare',
      'Makeup',
      'Hair Care',
      'Perfumes',
      'Health Supplements',
      'Medical Equipment',
      'Fitness Equipment',
      'Beauty Tools',
      'Natural Products',
      'Baby Care'
    ]
  },
  {
    name: 'Books',
    icon: '📚',
    subcategories: [
      'Textbooks',
      'Fiction',
      'Non-Fiction',
      'Childrens Books',
      'Academic Books',
      'Afrikaans Books',
      'Local Authors',
      'Religious Books',
      'Magazines',
      'Comics'
    ]
  },
  {
    name: 'Toys & Games',
    icon: '🧸',
    subcategories: [
      'Baby Toys',
      'Educational Toys',
      'Action Figures',
      'Board Games',
      'Video Games',
      'Outdoor Toys',
      'Arts & Crafts',
      'Building Blocks',
      'Dolls',
      'Remote Control Toys'
    ]
  },
  {
    name: 'Other',
    icon: '📦',
    subcategories: [
      'Musical Instruments',
      'Art & Collectibles',
      'Business Equipment',
      'Industrial Equipment',
      'Agricultural Equipment',
      'Construction Tools',
      'Pet Supplies',
      'Travel Gear',
      'Hobbies & Crafts',
      'Miscellaneous'
    ]
  }
]

// Extract just the category names for dropdowns
export const CATEGORY_NAMES = SOUTH_AFRICAN_CATEGORIES.map(cat => cat.name)

// Get subcategories for a specific category
export const getSubcategories = (categoryName: string): string[] => {
  const category = SOUTH_AFRICAN_CATEGORIES.find(cat => cat.name === categoryName)
  return category?.subcategories || []
}

// South African specific terms mapping
export const SA_TERMINOLOGY = {
  'pickup truck': 'bakkie',
  'pickup trucks': 'bakkies',
  'minivan': 'combi',
  'minivans': 'combis',
  'traffic light': 'robot',
  'traffic lights': 'robots',
  'barbecue': 'braai',
  'barbecues': 'braais',
  'soda': 'cool drink',
  'apartment': 'flat',
  'apartments': 'flats',
  'elevator': 'lift',
  'elevators': 'lifts',
  'flashlight': 'torch',
  'flashlights': 'torches',
  'cell phone': 'cellphone',
  'mobile phone': 'cellphone',
  'gas station': 'petrol station',
  'trunk': 'boot',
  'hood': 'bonnet',
  'faucet': 'tap',
  'faucets': 'taps'
}

// Convert text to use South African terminology
export const localizeSAText = (text: string): string => {
  let localizedText = text
  
  Object.entries(SA_TERMINOLOGY).forEach(([american, southAfrican]) => {
    const regex = new RegExp(`\\b${american}\\b`, 'gi')
    localizedText = localizedText.replace(regex, southAfrican)
  })
  
  return localizedText
}

// Common South African brands for automotive
export const SA_AUTOMOTIVE_BRANDS = [
  'Toyota', 'Ford', 'Volkswagen', 'BMW', 'Mercedes-Benz', 'Audi', 'Nissan',
  'Hyundai', 'Kia', 'Mazda', 'Honda', 'Chevrolet', 'Isuzu', 'Mitsubishi',
  'Subaru', 'Volvo', 'Land Rover', 'Jaguar', 'Peugeot', 'Renault',
  'Mahindra', 'Tata', 'GWM', 'Haval', 'Chery', 'JAC', 'Foton'
]

// Popular bakkie models in South Africa
export const SA_BAKKIE_MODELS = [
  'Toyota Hilux', 'Ford Ranger', 'Isuzu D-Max', 'Volkswagen Amarok',
  'Nissan Navara', 'Mitsubishi Triton', 'Mazda BT-50', 'GWM P-Series',
  'Mahindra Pik Up', 'JAC T6', 'Foton Tunland'
]
