import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import sharp from 'sharp'

export interface UploadResult {
  url: string
  filename: string
  size: number
  width: number
  height: number
}

export class ImageUploadService {
  private static readonly UPLOAD_DIR = './public/uploads'
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  private static readonly ALLOWED_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif',
    'image/bmp',
    'image/tiff',
    'image/tif',
    'image/svg+xml'
  ]
  private static readonly MAX_WIDTH = 1920
  private static readonly MAX_HEIGHT = 1920
  private static readonly QUALITY = 85

  /**
   * Initialize upload directory
   */
  static async init(): Promise<void> {
    if (!existsSync(this.UPLOAD_DIR)) {
      await mkdir(this.UPLOAD_DIR, { recursive: true })
    }
  }

  /**
   * Upload and process image file
   */
  static async uploadImage(file: File): Promise<UploadResult> {
    await this.init()

    // Validate file
    this.validateFile(file)

    // Generate unique filename
    const filename = this.generateFilename(file.name)
    const filepath = path.join(this.UPLOAD_DIR, filename)

    try {
      // Convert File to Buffer
      const buffer = Buffer.from(await file.arrayBuffer())

      // Process image with Sharp
      const processedImage = await this.processImage(buffer)

      // Save processed image
      await writeFile(filepath, processedImage.data)

      // Get the base URL for the full image URL
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'

      return {
        url: `${baseUrl}/uploads/${filename}`,
        filename,
        size: processedImage.data.length,
        width: processedImage.width,
        height: processedImage.height
      }
    } catch (error) {
      console.error('Error uploading image:', error)
      throw new Error('Failed to upload image')
    }
  }

  /**
   * Process image: resize, optimize, and convert to WebP
   */
  private static async processImage(buffer: Buffer): Promise<{
    data: Buffer
    width: number
    height: number
  }> {
    const image = sharp(buffer)
    const metadata = await image.metadata()

    // Calculate new dimensions while maintaining aspect ratio
    let { width = 0, height = 0 } = metadata
    
    if (width > this.MAX_WIDTH || height > this.MAX_HEIGHT) {
      const ratio = Math.min(this.MAX_WIDTH / width, this.MAX_HEIGHT / height)
      width = Math.round(width * ratio)
      height = Math.round(height * ratio)
    }

    // Process image
    const processedBuffer = await image
      .resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: this.QUALITY })
      .toBuffer()

    return {
      data: processedBuffer,
      width: width || 0,
      height: height || 0
    }
  }

  /**
   * Validate uploaded file
   */
  private static validateFile(file: File): void {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size too large. Maximum size is ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`)
    }

    // Check file type - be more flexible with MIME types
    const isValidType = this.ALLOWED_TYPES.includes(file.type) ||
                       file.type.startsWith('image/') ||
                       this.isValidImageExtension(file.name)

    if (!isValidType) {
      throw new Error(`Invalid file type. Please upload an image file (JPEG, PNG, WebP, GIF, BMP, TIFF, SVG)`)
    }
  }

  /**
   * Check if file has valid image extension
   */
  private static isValidImageExtension(filename: string): boolean {
    const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff', '.tif', '.svg']
    const extension = path.extname(filename).toLowerCase()
    return validExtensions.includes(extension)
  }

  /**
   * Generate unique filename
   */
  private static generateFilename(originalName: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    const extension = '.webp' // Always convert to WebP
    
    return `${timestamp}-${random}${extension}`
  }

  /**
   * Delete uploaded image
   */
  static async deleteImage(filename: string): Promise<void> {
    try {
      const filepath = path.join(this.UPLOAD_DIR, filename)
      const fs = await import('fs/promises')
      await fs.unlink(filepath)
    } catch (error) {
      console.error('Error deleting image:', error)
      // Don't throw - file might already be deleted
    }
  }
}

/**
 * Utility function to convert File to base64 for AI analysis
 */
export async function fileToBase64(file: File): Promise<string> {
  const buffer = Buffer.from(await file.arrayBuffer())
  return `data:${file.type};base64,${buffer.toString('base64')}`
}

/**
 * Utility function to get image dimensions
 */
export async function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  const buffer = Buffer.from(await file.arrayBuffer())
  const metadata = await sharp(buffer).metadata()
  
  return {
    width: metadata.width || 0,
    height: metadata.height || 0
  }
}
