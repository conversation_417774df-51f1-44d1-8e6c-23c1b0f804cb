'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Eye, 
  Users, 
  DollarSign,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { QuickStats } from '@/components/dashboard/StatsCard'
import ChartCard, { PlaceholderChart, ChartActions } from '@/components/dashboard/ChartCard'

interface AnalyticsData {
  revenue: {
    total: number
    thisMonth: number
    lastMonth: number
    growth: number
  }
  views: {
    total: number
    thisWeek: number
    lastWeek: number
    growth: number
  }
  conversions: {
    rate: number
    total: number
    thisMonth: number
  }
  topListings: Array<{
    id: string
    title: string
    views: number
    inquiries: number
    revenue: number
  }>
  trafficSources: Array<{
    source: string
    visitors: number
    percentage: number
  }>
}

export default function VendorAnalytics() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/login?message=Please sign in to access analytics&redirect=/dashboard/analytics')
      return
    }

    if (session.user.userType !== 'VENDOR') {
      router.push('/dashboard?message=Analytics requires vendor account')
      return
    }

    fetchAnalytics()
  }, [session, status, router, timeRange])

  const fetchAnalytics = async () => {
    try {
      const response = await fetch(`/api/dashboard/analytics?timeRange=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.analytics)
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout userType="VENDOR">
        <div className="p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!session || session.user.userType !== 'VENDOR') {
    return null
  }

  // Mock data for demonstration
  const mockAnalytics: AnalyticsData = {
    revenue: {
      total: 45750,
      thisMonth: 8500,
      lastMonth: 7200,
      growth: 18.1
    },
    views: {
      total: 12450,
      thisWeek: 890,
      lastWeek: 750,
      growth: 18.7
    },
    conversions: {
      rate: 3.2,
      total: 156,
      thisMonth: 28
    },
    topListings: [
      { id: '1', title: 'iPhone 12 Pro Max', views: 450, inquiries: 23, revenue: 8500 },
      { id: '2', title: 'MacBook Air M1', views: 380, inquiries: 18, revenue: 7200 },
      { id: '3', title: 'Samsung Galaxy S21', views: 320, inquiries: 15, revenue: 5800 },
      { id: '4', title: 'iPad Pro 11"', views: 290, inquiries: 12, revenue: 4500 },
      { id: '5', title: 'AirPods Pro', views: 250, inquiries: 10, revenue: 2800 }
    ],
    trafficSources: [
      { source: 'Direct', visitors: 2450, percentage: 45 },
      { source: 'Search', visitors: 1890, percentage: 35 },
      { source: 'Social Media', visitors: 650, percentage: 12 },
      { source: 'Referrals', visitors: 430, percentage: 8 }
    ]
  }

  const data = analytics || mockAnalytics

  const quickStats = [
    {
      title: 'Total Revenue',
      value: `R${data.revenue.total.toLocaleString()}`,
      icon: <DollarSign className="w-6 h-6" />,
      color: 'green' as const,
      change: {
        value: data.revenue.growth,
        type: data.revenue.growth >= 0 ? 'increase' as const : 'decrease' as const,
        period: 'vs last month'
      }
    },
    {
      title: 'Total Views',
      value: data.views.total.toLocaleString(),
      icon: <Eye className="w-6 h-6" />,
      color: 'blue' as const,
      change: {
        value: data.views.growth,
        type: data.views.growth >= 0 ? 'increase' as const : 'decrease' as const,
        period: 'vs last week'
      }
    },
    {
      title: 'Conversion Rate',
      value: `${data.conversions.rate}%`,
      icon: <TrendingUp className="w-6 h-6" />,
      color: 'purple' as const,
      subtitle: `${data.conversions.thisMonth} this month`
    },
    {
      title: 'Avg. Revenue/Listing',
      value: `R${Math.round(data.revenue.total / data.topListings.length).toLocaleString()}`,
      icon: <BarChart3 className="w-6 h-6" />,
      color: 'orange' as const,
      subtitle: 'Per active listing'
    }
  ]

  return (
    <DashboardLayout userType="VENDOR">
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics 📊</h1>
            <p className="text-gray-600 mt-2">
              Detailed insights into your business performance
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <button
              onClick={fetchAnalytics}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mb-8">
          <QuickStats stats={quickStats} loading={loading} />
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Revenue Chart */}
          <ChartCard
            title="Revenue Trends"
            subtitle="Monthly revenue over time"
            actions={<ChartActions />}
          >
            <PlaceholderChart 
              type="bar" 
              title="Revenue Chart" 
              description="Track your monthly revenue performance"
            />
          </ChartCard>

          {/* Views Chart */}
          <ChartCard
            title="Views & Traffic"
            subtitle="Daily views and visitor trends"
            actions={<ChartActions />}
          >
            <PlaceholderChart 
              type="line" 
              title="Traffic Chart" 
              description="Monitor your listing views and traffic"
            />
          </ChartCard>
        </div>

        {/* Performance Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Top Performing Listings */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Top Performing Listings</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {data.topListings.map((listing, index) => (
                  <div key={listing.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{listing.title}</h4>
                        <p className="text-sm text-gray-600">
                          {listing.views} views • {listing.inquiries} inquiries
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">R{listing.revenue.toLocaleString()}</p>
                      <p className="text-sm text-gray-500">Revenue</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Traffic Sources */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Traffic Sources</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {data.trafficSources.map((source) => (
                  <div key={source.source} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="font-medium text-gray-900">{source.source}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">{source.visitors.toLocaleString()}</span>
                      <span className="text-sm font-medium text-gray-900">{source.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
