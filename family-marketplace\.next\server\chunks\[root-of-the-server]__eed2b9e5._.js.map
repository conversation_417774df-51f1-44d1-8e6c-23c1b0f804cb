{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          },\n          include: {\n            vendorProfile: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          userType: user.userType,\n          isVerified: user.isVerified,\n          avatar: user.avatar,\n          vendorProfile: user.vendorProfile\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n        token.username = user.username\n        token.userType = user.userType\n        token.isVerified = user.isVerified\n        token.vendorProfile = user.vendorProfile\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n        session.user.username = token.username as string\n        session.user.userType = token.userType as string\n        session.user.isVerified = token.isVerified as boolean\n        session.user.vendorProfile = token.vendorProfile as any\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/login',\n    signUp: '/register'\n  }\n}\n\n// Helper functions for user management\nexport async function createUser(userData: {\n  email: string\n  username: string\n  password: string\n  firstName?: string\n  lastName?: string\n  userType: 'PRIVATE' | 'VENDOR'\n  vendorData?: {\n    businessName: string\n    description?: string\n    website?: string\n  }\n}) {\n  const hashedPassword = await bcrypt.hash(userData.password, 12)\n\n  const user = await prisma.user.create({\n    data: {\n      email: userData.email,\n      username: userData.username,\n      password: hashedPassword,\n      firstName: userData.firstName,\n      lastName: userData.lastName,\n      userType: userData.userType,\n      vendorProfile: userData.userType === 'VENDOR' && userData.vendorData ? {\n        create: {\n          businessName: userData.vendorData.businessName,\n          description: userData.vendorData.description,\n          website: userData.vendorData.website\n        }\n      } : undefined\n    },\n    include: {\n      vendorProfile: true\n    }\n  })\n\n  return user\n}\n\nexport async function getUserByEmail(email: string) {\n  return await prisma.user.findUnique({\n    where: { email },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n\nexport async function getUserByUsername(username: string) {\n  return await prisma.user.findUnique({\n    where: { username },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;oBACA,SAAS;wBACP,eAAe;oBACjB;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,aAAa;gBACnC;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,aAAa,GAAG,KAAK,aAAa;YAC1C;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;YAClD;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,eAAe,WAAW,QAYhC;IACC,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;IAE5D,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM;YACJ,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;YAC3B,UAAU;YACV,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;YAC3B,eAAe,SAAS,QAAQ,KAAK,YAAY,SAAS,UAAU,GAAG;gBACrE,QAAQ;oBACN,cAAc,SAAS,UAAU,CAAC,YAAY;oBAC9C,aAAa,SAAS,UAAU,CAAC,WAAW;oBAC5C,SAAS,SAAS,UAAU,CAAC,OAAO;gBACtC;YACF,IAAI;QACN;QACA,SAAS;YACP,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,KAAa;IAChD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAM;QACf,SAAS;YACP,eAAe;QACjB;IACF;AACF;AAEO,eAAe,kBAAkB,QAAgB;IACtD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,eAAe;QACjB;IACF;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/user/listings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      )\n    }\n\n    const listings = await prisma.listing.findMany({\n      where: {\n        userId: session.user.id\n      },\n      include: {\n        images: {\n          orderBy: { order: 'asc' }\n        },\n        _count: {\n          select: {\n            messages: true,\n            favorites: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      listings\n    })\n  } catch (error) {\n    console.error('User listings fetch error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;YACA,SAAS;gBACP,QAAQ;oBACN,SAAS;wBAAE,OAAO;oBAAM;gBAC1B;gBACA,QAAQ;oBACN,QAAQ;wBACN,UAAU;wBACV,WAAW;oBACb;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}