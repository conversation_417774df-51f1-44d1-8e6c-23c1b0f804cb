import { 
  Shield, 
  Eye, 
  Users, 
  MapPin, 
  CreditCard, 
  Phone, 
  AlertTriangle,
  CheckCircle,
  MessageCircle,
  Lock
} from 'lucide-react'

export default function SafetyPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-600 to-blue-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Safety First
            </h1>
            <p className="text-xl md:text-2xl text-green-100 mb-8 max-w-3xl mx-auto">
              Your safety is our top priority. Learn how to buy and sell safely on Family Marketplace.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Safety Guidelines */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Essential Safety Guidelines</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Follow these important safety tips to protect yourself when buying and selling online
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {/* Meeting Safely */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <MapPin className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">Meet in Public Places</h3>
            <ul className="text-gray-600 text-sm space-y-2">
              <li>• Choose busy, well-lit public locations</li>
              <li>• Shopping centers and malls are ideal</li>
              <li>• Avoid meeting at private homes</li>
              <li>• Consider police station parking lots</li>
              <li>• Meet during daylight hours when possible</li>
            </ul>
          </div>

          {/* Verify Identity */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Eye className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">Verify Before Meeting</h3>
            <ul className="text-gray-600 text-sm space-y-2">
              <li>• Check user profiles and ratings</li>
              <li>• Read reviews from other users</li>
              <li>• Verify phone numbers when possible</li>
              <li>• Look for verified business badges</li>
              <li>• Trust your instincts about users</li>
            </ul>
          </div>

          {/* Bring Support */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">Bring a Friend</h3>
            <ul className="text-gray-600 text-sm space-y-2">
              <li>• Bring a friend or family member</li>
              <li>• Let someone know where you're going</li>
              <li>• Share meeting details with trusted contacts</li>
              <li>• Keep your phone charged and accessible</li>
              <li>• Have an exit plan if needed</li>
            </ul>
          </div>

          {/* Secure Payments */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="bg-orange-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <CreditCard className="w-6 h-6 text-orange-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">Safe Payment Methods</h3>
            <ul className="text-gray-600 text-sm space-y-2">
              <li>• Cash is often the safest option</li>
              <li>• Use bank transfers for high-value items</li>
              <li>• Avoid wire transfers to strangers</li>
              <li>• Never share banking passwords</li>
              <li>• Get receipts for all transactions</li>
            </ul>
          </div>

          {/* Communication */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="bg-indigo-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <MessageCircle className="w-6 h-6 text-indigo-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">Use Platform Messaging</h3>
            <ul className="text-gray-600 text-sm space-y-2">
              <li>• Keep conversations on our platform</li>
              <li>• Don't share personal information early</li>
              <li>• Be wary of urgent pressure tactics</li>
              <li>• Report suspicious messages</li>
              <li>• Save important conversation records</li>
            </ul>
          </div>

          {/* Trust Your Instincts */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="bg-red-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">Trust Your Instincts</h3>
            <ul className="text-gray-600 text-sm space-y-2">
              <li>• If something feels wrong, walk away</li>
              <li>• Don't ignore red flags</li>
              <li>• Be cautious of deals that seem too good</li>
              <li>• Report suspicious behavior immediately</li>
              <li>• Your safety is more important than any deal</li>
            </ul>
          </div>
        </div>

        {/* Red Flags Section */}
        <div className="bg-red-50 border border-red-200 rounded-2xl p-8 mb-16">
          <div className="text-center mb-8">
            <div className="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Warning Signs to Watch For</h2>
            <p className="text-xl text-gray-600">
              Be alert to these common scam tactics and suspicious behaviors
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">🚨 Immediate Red Flags</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  Requests for payment before meeting
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  Pressure to act quickly or urgently
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  Prices significantly below market value
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  Refusal to meet in person
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  Poor grammar or generic responses
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  Requests for personal financial information
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">⚠️ Suspicious Behaviors</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  New accounts with no history
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Stock photos instead of real images
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Vague or inconsistent item descriptions
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Reluctance to answer specific questions
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Multiple similar items from one seller
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 mr-2">•</span>
                  Requests to communicate off-platform
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Platform Safety Features */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Safety Features</h2>
            <p className="text-xl text-gray-600">
              We've built multiple layers of protection into our platform
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">User Verification</h3>
              <p className="text-gray-600 text-sm">
                Email and phone verification for all users, with additional business verification for vendors
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Eye className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">AI Monitoring</h3>
              <p className="text-gray-600 text-sm">
                Our AI systems monitor listings and conversations for suspicious activity
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Rating System</h3>
              <p className="text-gray-600 text-sm">
                User ratings and reviews help build trust and identify reliable traders
              </p>
            </div>

            <div className="text-center">
              <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">24/7 Support</h3>
              <p className="text-gray-600 text-sm">
                Report issues anytime through our support system for quick response
              </p>
            </div>
          </div>
        </div>

        {/* Reporting Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Report Suspicious Activity</h2>
            <p className="text-xl text-gray-600">
              Help us keep the community safe by reporting problems
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 text-center">
              <div className="bg-red-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Suspicious Listings</h3>
              <p className="text-gray-600 text-sm mb-4">
                Report listings that seem fake, stolen, or violate our policies
              </p>
              <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                Report Listing
              </button>
            </div>

            <div className="bg-white rounded-xl p-6 text-center">
              <div className="bg-orange-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Problem Users</h3>
              <p className="text-gray-600 text-sm mb-4">
                Report users who are harassing, scamming, or behaving inappropriately
              </p>
              <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                Report User
              </button>
            </div>

            <div className="bg-white rounded-xl p-6 text-center">
              <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Safety Concerns</h3>
              <p className="text-gray-600 text-sm mb-4">
                Contact us about any safety concerns or suggestions for improvement
              </p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Contact Support
              </button>
            </div>
          </div>
        </div>

        {/* Emergency Contacts */}
        <div className="bg-gray-900 text-white rounded-2xl p-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Emergency Contacts</h2>
          <p className="text-xl text-gray-300 mb-8">
            If you're in immediate danger, contact emergency services first
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-bold mb-2">🚨 Emergency Services</h3>
              <p className="text-2xl font-bold text-red-400">10111</p>
              <p className="text-gray-400">Police Emergency</p>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-2">📞 Crime Stop</h3>
              <p className="text-2xl font-bold text-orange-400">08600 10111</p>
              <p className="text-gray-400">Anonymous Crime Reporting</p>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-2">💬 Platform Support</h3>
              <p className="text-2xl font-bold text-blue-400">+27 11 123 4567</p>
              <p className="text-gray-400">Family Marketplace Safety Team</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
