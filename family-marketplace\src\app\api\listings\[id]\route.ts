import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const listing = await prisma.listing.findUnique({
      where: {
        id: params.id,
        status: 'ACTIVE'
      },
      include: {
        images: {
          orderBy: { order: 'asc' }
        },
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            userType: true,
            isVerified: true,
            createdAt: true,
            vendorProfile: {
              select: {
                businessName: true,
                description: true,
                verified: true
              }
            }
          }
        }
      }
    })

    if (!listing) {
      return NextResponse.json(
        { error: 'Listing not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      listing
    })
  } catch (error) {
    console.error('Listing fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      title,
      description,
      price,
      condition,
      category,
      subcategory,
      brand,
      model,
      location
    } = body

    // TODO: Add authentication check
    // const session = await getServerSession(authOptions)
    // if (!session?.user?.id) {
    //   return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    // }

    const listing = await prisma.listing.findUnique({
      where: { id: params.id }
    })

    if (!listing) {
      return NextResponse.json(
        { error: 'Listing not found' },
        { status: 404 }
      )
    }

    // TODO: Check if user owns the listing
    // if (listing.userId !== session.user.id) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    // }

    const updatedListing = await prisma.listing.update({
      where: { id: params.id },
      data: {
        title,
        description,
        price: parseFloat(price),
        condition,
        category,
        subcategory: subcategory || null,
        brand: brand || null,
        model: model || null,
        location: location || 'South Africa'
      },
      include: {
        images: {
          orderBy: { order: 'asc' }
        },
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            userType: true,
            vendorProfile: {
              select: {
                businessName: true,
                verified: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Listing updated successfully',
      listing: updatedListing
    })
  } catch (error) {
    console.error('Listing update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // TODO: Add authentication check
    // const session = await getServerSession(authOptions)
    // if (!session?.user?.id) {
    //   return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    // }

    const listing = await prisma.listing.findUnique({
      where: { id: params.id }
    })

    if (!listing) {
      return NextResponse.json(
        { error: 'Listing not found' },
        { status: 404 }
      )
    }

    // TODO: Check if user owns the listing
    // if (listing.userId !== session.user.id) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    // }

    await prisma.listing.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Listing deleted successfully'
    })
  } catch (error) {
    console.error('Listing deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
