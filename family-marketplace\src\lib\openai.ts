import OpenAI from 'openai'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY is not set in environment variables')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// AI Service Types
export interface ImageAnalysisResult {
  category: string
  subcategory?: string
  title: string
  description: string
  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'
  brand?: string
  model?: string
  tags: string[]
  confidence: number
}

export interface PriceSuggestion {
  suggestedPrice: number
  priceRange: {
    min: number
    max: number
  }
  confidence: number
  reasoning: string
}

// Core AI Services
export class AIService {
  /**
   * Analyze an image to extract product information
   */
  static async analyzeImage(imageUrl: string): Promise<ImageAnalysisResult> {
    // If it's a localhost URL, convert to base64
    if (imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
      return this.analyzeImageFromLocalhost(imageUrl)
    }
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Analyze this product image for a South African marketplace listing. Extract information in JSON format:
                {
                  "category": "Main category: Electronics, Furniture, Clothing, Books, Sports & Outdoors, Home & Garden, Toys & Games, Automotive, Health & Beauty, or Other",
                  "subcategory": "Specific subcategory if applicable",
                  "title": "Descriptive title suitable for South African buyers",
                  "description": "Detailed description highlighting key features and condition",
                  "condition": "Realistic condition: NEW, LIKE_NEW, GOOD, FAIR, or POOR",
                  "brand": "Brand name if visible/identifiable",
                  "model": "Model name/number if visible",
                  "tags": ["relevant", "search", "keywords", "for", "SA", "market"],
                  "confidence": 0.95
                }

                Guidelines:
                - Focus on details visible in the image
                - Consider South African market preferences
                - Be realistic about condition assessment
                - Include relevant search terms for local buyers
                - If uncertain about any field, use null`
              },
              {
                type: "image_url",
                image_url: {
                  url: imageUrl
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response from OpenAI')
      }

      // Parse JSON response (handle markdown code blocks)
      const cleanContent = this.extractJsonFromResponse(content)
      const result = JSON.parse(cleanContent) as ImageAnalysisResult
      
      // Log analytics
      await this.logAnalytics('image_analysis', { imageUrl }, result, result.confidence, 'gpt-4-vision-preview')
      
      return result
    } catch (error) {
      console.error('Error analyzing image:', error)

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('Invalid image')) {
          throw new Error('The image format is not supported or the image is corrupted')
        }
        if (error.message.includes('rate limit') || error.message.includes('429') || error.message.includes('quota')) {
          throw new Error('🚫 AI service quota exceeded. Please check your OpenAI billing or try again later.')
        }
        if (error.message.includes('API key')) {
          throw new Error('AI service configuration error')
        }
      }

      throw new Error('Failed to analyze image. Please try again.')
    }
  }

  /**
   * Analyze image from localhost by converting to base64
   */
  static async analyzeImageFromLocalhost(imageUrl: string): Promise<ImageAnalysisResult> {
    try {
      // Fetch the image from localhost
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error('Failed to fetch image from localhost')
      }

      const buffer = await response.arrayBuffer()
      const base64 = Buffer.from(buffer).toString('base64')
      const mimeType = response.headers.get('content-type') || 'image/jpeg'
      const base64Url = `data:${mimeType};base64,${base64}`

      const openaiResponse = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Analyze this product image for a South African marketplace listing. Extract information in JSON format:
                {
                  "category": "Main category: Electronics, Furniture, Clothing, Books, Sports & Outdoors, Home & Garden, Toys & Games, Automotive, Health & Beauty, or Other",
                  "subcategory": "Specific subcategory if applicable",
                  "title": "Descriptive title suitable for South African buyers",
                  "description": "Detailed description highlighting key features and condition",
                  "condition": "Realistic condition: NEW, LIKE_NEW, GOOD, FAIR, or POOR",
                  "brand": "Brand name if visible/identifiable",
                  "model": "Model name/number if visible",
                  "tags": ["relevant", "search", "keywords", "for", "SA", "market"],
                  "confidence": 0.95
                }

                Guidelines:
                - Focus on details visible in the image
                - Consider South African market preferences
                - Be realistic about condition assessment
                - Include relevant search terms for local buyers
                - If uncertain about any field, use null`
              },
              {
                type: "image_url",
                image_url: {
                  url: base64Url,
                  detail: "high"
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      })

      const content = openaiResponse.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response from OpenAI')
      }

      // Parse JSON response (handle markdown code blocks)
      const cleanContent = this.extractJsonFromResponse(content)
      const result = JSON.parse(cleanContent) as ImageAnalysisResult

      // Log analytics
      await this.logAnalytics('image_analysis', { imageUrl }, result, result.confidence, 'gpt-4o')

      return result
    } catch (error) {
      console.error('Error analyzing localhost image:', error)

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('Invalid image')) {
          throw new Error('The image format is not supported or the image is corrupted')
        }
        if (error.message.includes('rate limit') || error.message.includes('429') || error.message.includes('quota')) {
          throw new Error('🚫 AI service quota exceeded. Please check your OpenAI billing or try again later.')
        }
        if (error.message.includes('API key')) {
          throw new Error('AI service configuration error')
        }
      }

      throw new Error('Failed to analyze image. Please try again.')
    }
  }

  /**
   * Generate price suggestions based on product details and market data
   */
  static async suggestPrice(productInfo: {
    category: string
    subcategory?: string
    brand?: string
    model?: string
    condition: string
    description: string
  }): Promise<PriceSuggestion> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a South African marketplace pricing expert. Suggest realistic prices in South African Rand (ZAR) based on local market conditions, brand reputation, item condition, and economic factors specific to South Africa."
          },
          {
            role: "user",
            content: `Suggest a realistic price for this product in the South African market (ZAR):

            Product Details:
            - Category: ${productInfo.category}
            - Subcategory: ${productInfo.subcategory || 'N/A'}
            - Brand: ${productInfo.brand || 'N/A'}
            - Model: ${productInfo.model || 'N/A'}
            - Condition: ${productInfo.condition}
            - Description: ${productInfo.description}

            Consider South African factors:
            - Local market demand and supply
            - Brand availability and reputation in SA
            - Economic conditions and purchasing power
            - Import costs and local alternatives
            - Typical depreciation rates for used items

            Return JSON format (prices in ZAR):
            {
              "suggestedPrice": 1500,
              "priceRange": {
                "min": 1200,
                "max": 1800
              },
              "confidence": 0.85,
              "reasoning": "Brief explanation considering SA market factors"
            }`
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response from OpenAI')
      }

      const cleanContent = this.extractJsonFromResponse(content)
      const result = JSON.parse(cleanContent) as PriceSuggestion
      
      // Log analytics
      await this.logAnalytics('price_suggestion', productInfo, result, result.confidence, 'gpt-4')
      
      return result
    } catch (error) {
      console.error('Error suggesting price:', error)
      throw new Error('Failed to suggest price')
    }
  }

  /**
   * Generate improved product descriptions
   */
  static async enhanceDescription(originalDescription: string, productInfo: any): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a copywriting expert specializing in marketplace listings. Create compelling, accurate product descriptions that highlight key features and benefits while maintaining honesty about condition and details."
          },
          {
            role: "user",
            content: `Enhance this product description to make it more appealing and informative:

            Original: "${originalDescription}"
            
            Product Info: ${JSON.stringify(productInfo)}
            
            Guidelines:
            - Keep it honest and accurate
            - Highlight key features and benefits
            - Use engaging but professional language
            - Include relevant keywords for searchability
            - Mention condition appropriately
            - Keep it concise but informative (2-3 paragraphs max)
            
            Return only the enhanced description, no JSON or extra formatting.`
          }
        ],
        max_tokens: 400,
        temperature: 0.7
      })

      const enhancedDescription = response.choices[0]?.message?.content?.trim()
      if (!enhancedDescription) {
        throw new Error('No response from OpenAI')
      }

      // Log analytics
      await this.logAnalytics('description_enhancement', { originalDescription, productInfo }, { enhancedDescription }, 0.9, 'gpt-4')
      
      return enhancedDescription
    } catch (error) {
      console.error('Error enhancing description:', error)
      return originalDescription // Fallback to original
    }
  }

  /**
   * Generate auto-responses for common buyer questions
   */
  static async generateAutoResponse(question: string, listingInfo: any): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a helpful assistant for a marketplace seller. Generate polite, informative responses to buyer questions based on the listing information. Be helpful but direct sellers to contact for specific details not in the listing."
          },
          {
            role: "user",
            content: `Generate a response to this buyer question:

            Question: "${question}"
            
            Listing Info: ${JSON.stringify(listingInfo)}
            
            Guidelines:
            - Be polite and helpful
            - Use information from the listing when available
            - For specific details not in listing, suggest contacting the seller
            - Keep responses concise but friendly
            - Don't make up information not in the listing
            
            Return only the response message.`
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      })

      const autoResponse = response.choices[0]?.message?.content?.trim()
      if (!autoResponse) {
        throw new Error('No response from OpenAI')
      }

      // Log analytics
      await this.logAnalytics('auto_response', { question, listingInfo }, { autoResponse }, 0.8, 'gpt-4')
      
      return autoResponse
    } catch (error) {
      console.error('Error generating auto response:', error)
      return "Thank you for your question! Please feel free to contact the seller directly for more specific details about this item."
    }
  }

  /**
   * Extract JSON from markdown-wrapped responses
   */
  private static extractJsonFromResponse(content: string): string {
    // Remove markdown code blocks if present
    const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/)
    if (jsonMatch) {
      return jsonMatch[1].trim()
    }

    // If no markdown blocks, try to find JSON object
    const jsonObjectMatch = content.match(/\{[\s\S]*\}/)
    if (jsonObjectMatch) {
      return jsonObjectMatch[0].trim()
    }

    // Return original content if no JSON found
    return content.trim()
  }

  /**
   * Log AI analytics for tracking and optimization
   */
  private static async logAnalytics(
    type: string,
    input: any,
    output: any,
    confidence: number,
    model: string
  ): Promise<void> {
    try {
      const { prisma } = await import('./prisma')
      
      await prisma.aIAnalytics.create({
        data: {
          type,
          input,
          output,
          confidence,
          model,
          cost: this.estimateCost(type, model) // Rough cost estimation
        }
      })
    } catch (error) {
      console.error('Error logging analytics:', error)
      // Don't throw - analytics logging shouldn't break the main functionality
    }
  }

  /**
   * Estimate API costs for budgeting
   */
  private static estimateCost(type: string, model: string): number {
    // Rough cost estimates based on OpenAI pricing (as of 2024)
    const costs = {
      'gpt-4': 0.03, // per 1K tokens
      'gpt-4-vision-preview': 0.01, // per image + text tokens
      'gpt-3.5-turbo': 0.002 // per 1K tokens
    }
    
    const baseCost = costs[model as keyof typeof costs] || 0.01
    
    // Multiply by estimated token usage
    const tokenMultipliers = {
      'image_analysis': 800,
      'price_suggestion': 400,
      'description_enhancement': 300,
      'auto_response': 150
    }
    
    const tokens = tokenMultipliers[type as keyof typeof tokenMultipliers] || 200
    return (baseCost * tokens) / 1000
  }
}
