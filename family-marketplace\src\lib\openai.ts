import OpenAI from 'openai'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY is not set in environment variables')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// AI Service Types
export interface ImageAnalysisResult {
  category: string
  subcategory?: string
  title: string
  description: string
  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'
  brand?: string
  model?: string
  tags: string[]
  confidence: number
}

export interface PriceSuggestion {
  suggestedPrice: number
  priceRange: {
    min: number
    max: number
  }
  confidence: number
  reasoning: string
}

// Core AI Services
export class AIService {
  /**
   * Analyze an image to extract product information
   */
  static async analyzeImage(imageUrl: string): Promise<ImageAnalysisResult> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Analyze this product image for a South African marketplace listing. Extract information in JSON format:
                {
                  "category": "Main category: Electronics, Furniture, Clothing, Books, Sports & Outdoors, Home & Garden, Toys & Games, Automotive, Health & Beauty, or Other",
                  "subcategory": "Specific subcategory if applicable",
                  "title": "Descriptive title suitable for South African buyers",
                  "description": "Detailed description highlighting key features and condition",
                  "condition": "Realistic condition: NEW, LIKE_NEW, GOOD, FAIR, or POOR",
                  "brand": "Brand name if visible/identifiable",
                  "model": "Model name/number if visible",
                  "tags": ["relevant", "search", "keywords", "for", "SA", "market"],
                  "confidence": 0.95
                }

                Guidelines:
                - Focus on details visible in the image
                - Consider South African market preferences
                - Be realistic about condition assessment
                - Include relevant search terms for local buyers
                - If uncertain about any field, use null`
              },
              {
                type: "image_url",
                image_url: {
                  url: imageUrl
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response from OpenAI')
      }

      // Parse JSON response
      const result = JSON.parse(content) as ImageAnalysisResult
      
      // Log analytics
      await this.logAnalytics('image_analysis', { imageUrl }, result, result.confidence, 'gpt-4-vision-preview')
      
      return result
    } catch (error) {
      console.error('Error analyzing image:', error)
      throw new Error('Failed to analyze image')
    }
  }

  /**
   * Generate price suggestions based on product details and market data
   */
  static async suggestPrice(productInfo: {
    category: string
    subcategory?: string
    brand?: string
    model?: string
    condition: string
    description: string
  }): Promise<PriceSuggestion> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a South African marketplace pricing expert. Suggest realistic prices in South African Rand (ZAR) based on local market conditions, brand reputation, item condition, and economic factors specific to South Africa."
          },
          {
            role: "user",
            content: `Suggest a realistic price for this product in the South African market (ZAR):

            Product Details:
            - Category: ${productInfo.category}
            - Subcategory: ${productInfo.subcategory || 'N/A'}
            - Brand: ${productInfo.brand || 'N/A'}
            - Model: ${productInfo.model || 'N/A'}
            - Condition: ${productInfo.condition}
            - Description: ${productInfo.description}

            Consider South African factors:
            - Local market demand and supply
            - Brand availability and reputation in SA
            - Economic conditions and purchasing power
            - Import costs and local alternatives
            - Typical depreciation rates for used items

            Return JSON format (prices in ZAR):
            {
              "suggestedPrice": 1500,
              "priceRange": {
                "min": 1200,
                "max": 1800
              },
              "confidence": 0.85,
              "reasoning": "Brief explanation considering SA market factors"
            }`
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response from OpenAI')
      }

      const result = JSON.parse(content) as PriceSuggestion
      
      // Log analytics
      await this.logAnalytics('price_suggestion', productInfo, result, result.confidence, 'gpt-4')
      
      return result
    } catch (error) {
      console.error('Error suggesting price:', error)
      throw new Error('Failed to suggest price')
    }
  }

  /**
   * Generate improved product descriptions
   */
  static async enhanceDescription(originalDescription: string, productInfo: any): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a copywriting expert specializing in marketplace listings. Create compelling, accurate product descriptions that highlight key features and benefits while maintaining honesty about condition and details."
          },
          {
            role: "user",
            content: `Enhance this product description to make it more appealing and informative:

            Original: "${originalDescription}"
            
            Product Info: ${JSON.stringify(productInfo)}
            
            Guidelines:
            - Keep it honest and accurate
            - Highlight key features and benefits
            - Use engaging but professional language
            - Include relevant keywords for searchability
            - Mention condition appropriately
            - Keep it concise but informative (2-3 paragraphs max)
            
            Return only the enhanced description, no JSON or extra formatting.`
          }
        ],
        max_tokens: 400,
        temperature: 0.7
      })

      const enhancedDescription = response.choices[0]?.message?.content?.trim()
      if (!enhancedDescription) {
        throw new Error('No response from OpenAI')
      }

      // Log analytics
      await this.logAnalytics('description_enhancement', { originalDescription, productInfo }, { enhancedDescription }, 0.9, 'gpt-4')
      
      return enhancedDescription
    } catch (error) {
      console.error('Error enhancing description:', error)
      return originalDescription // Fallback to original
    }
  }

  /**
   * Generate auto-responses for common buyer questions
   */
  static async generateAutoResponse(question: string, listingInfo: any): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a helpful assistant for a marketplace seller. Generate polite, informative responses to buyer questions based on the listing information. Be helpful but direct sellers to contact for specific details not in the listing."
          },
          {
            role: "user",
            content: `Generate a response to this buyer question:

            Question: "${question}"
            
            Listing Info: ${JSON.stringify(listingInfo)}
            
            Guidelines:
            - Be polite and helpful
            - Use information from the listing when available
            - For specific details not in listing, suggest contacting the seller
            - Keep responses concise but friendly
            - Don't make up information not in the listing
            
            Return only the response message.`
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      })

      const autoResponse = response.choices[0]?.message?.content?.trim()
      if (!autoResponse) {
        throw new Error('No response from OpenAI')
      }

      // Log analytics
      await this.logAnalytics('auto_response', { question, listingInfo }, { autoResponse }, 0.8, 'gpt-4')
      
      return autoResponse
    } catch (error) {
      console.error('Error generating auto response:', error)
      return "Thank you for your question! Please feel free to contact the seller directly for more specific details about this item."
    }
  }

  /**
   * Log AI analytics for tracking and optimization
   */
  private static async logAnalytics(
    type: string,
    input: any,
    output: any,
    confidence: number,
    model: string
  ): Promise<void> {
    try {
      const { prisma } = await import('./prisma')
      
      await prisma.aIAnalytics.create({
        data: {
          type,
          input,
          output,
          confidence,
          model,
          cost: this.estimateCost(type, model) // Rough cost estimation
        }
      })
    } catch (error) {
      console.error('Error logging analytics:', error)
      // Don't throw - analytics logging shouldn't break the main functionality
    }
  }

  /**
   * Estimate API costs for budgeting
   */
  private static estimateCost(type: string, model: string): number {
    // Rough cost estimates based on OpenAI pricing (as of 2024)
    const costs = {
      'gpt-4': 0.03, // per 1K tokens
      'gpt-4-vision-preview': 0.01, // per image + text tokens
      'gpt-3.5-turbo': 0.002 // per 1K tokens
    }
    
    const baseCost = costs[model as keyof typeof costs] || 0.01
    
    // Multiply by estimated token usage
    const tokenMultipliers = {
      'image_analysis': 800,
      'price_suggestion': 400,
      'description_enhancement': 300,
      'auto_response': 150
    }
    
    const tokens = tokenMultipliers[type as keyof typeof tokenMultipliers] || 200
    return (baseCost * tokens) / 1000
  }
}
