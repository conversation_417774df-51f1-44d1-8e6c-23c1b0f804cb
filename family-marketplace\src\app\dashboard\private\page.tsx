'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  Package, 
  Heart, 
  MessageCircle, 
  Eye, 
  Plus,
  TrendingUp,
  Star,
  Calendar,
  DollarSign,
  Search
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { QuickStats } from '@/components/dashboard/StatsCard'
import ChartCard, { PlaceholderChart, ChartActions } from '@/components/dashboard/ChartCard'
import ActivityFeed from '@/components/dashboard/ActivityFeed'

interface PrivateDashboardStats {
  totalListings: number
  activeListings: number
  totalViews: number
  totalMessages: number
  totalFavorites: number
  averagePrice: number
  recentActivity: Array<{
    id: string
    type: 'listing' | 'message' | 'favorite' | 'view'
    title: string
    description?: string
    timestamp: string
    metadata?: {
      price?: number
      status?: string
    }
    href?: string
  }>
  recentListings: Array<{
    id: string
    title: string
    price: number
    status: string
    views: number
    favorites: number
    createdAt: string
    images: string[]
  }>
}

export default function PrivateDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<PrivateDashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/login?message=Please sign in to access dashboard&redirect=/dashboard/private')
      return
    }

    fetchDashboardData()
  }, [session, status, router, timeRange])

  const fetchDashboardData = async () => {
    try {
      const response = await fetch(`/api/dashboard/private?timeRange=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout userType="PRIVATE">
        <div className="p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!session) {
    return null
  }

  const quickStats = [
    {
      title: 'Active Listings',
      value: stats?.activeListings || 0,
      icon: <Package className="w-6 h-6" />,
      color: 'blue' as const,
      change: stats?.activeListings ? {
        value: 12,
        type: 'increase' as const,
        period: 'this month'
      } : undefined
    },
    {
      title: 'Total Views',
      value: stats?.totalViews || 0,
      icon: <Eye className="w-6 h-6" />,
      color: 'green' as const,
      change: stats?.totalViews ? {
        value: 8,
        type: 'increase' as const,
        period: 'this week'
      } : undefined
    },
    {
      title: 'Messages',
      value: stats?.totalMessages || 0,
      icon: <MessageCircle className="w-6 h-6" />,
      color: 'purple' as const,
      subtitle: 'Unread: 3'
    },
    {
      title: 'Favorites',
      value: stats?.totalFavorites || 0,
      icon: <Heart className="w-6 h-6" />,
      color: 'red' as const,
      subtitle: 'On your listings'
    }
  ]

  return (
    <DashboardLayout userType="PRIVATE">
      <div>
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome back, {session.user.firstName || session.user.username}! 👋
              </h1>
              <p className="text-gray-600 mt-2">
                Here's what's happening with your listings
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
              <Link
                href="/create"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2 shadow-lg"
              >
                <Plus className="w-4 h-4" />
                Create Listing
              </Link>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mb-8">
          <QuickStats stats={quickStats} loading={loading} />
        </div>

        {/* Charts and Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Views Chart */}
          <ChartCard
            title="Listing Views"
            subtitle="Views over time"
            actions={<ChartActions />}
          >
            <PlaceholderChart 
              type="line" 
              title="Views Trend" 
              description="Track how your listings are performing"
            />
          </ChartCard>

          {/* Recent Activity */}
          <ActivityFeed
            title="Recent Activity"
            items={stats?.recentActivity || []}
            loading={loading}
            emptyMessage="No recent activity to show"
          />
        </div>

        {/* Recent Listings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Your Recent Listings</h3>
              <Link
                href="/listings"
                className="text-blue-600 hover:text-blue-700 font-medium text-sm"
              >
                View all listings
              </Link>
            </div>
          </div>
          <div className="p-6">
            {loading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : stats?.recentListings && stats.recentListings.length > 0 ? (
              <div className="space-y-4">
                {stats.recentListings.slice(0, 5).map((listing) => (
                  <div key={listing.id} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                      {listing.images[0] ? (
                        <img 
                          src={listing.images[0]} 
                          alt={listing.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          📷
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <Link 
                        href={`/listings/${listing.id}`}
                        className="text-sm font-medium text-gray-900 hover:text-blue-600"
                      >
                        {listing.title}
                      </Link>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-sm font-semibold text-green-600">
                          ${listing.price.toLocaleString()}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          listing.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                          listing.status === 'SOLD' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {listing.status}
                        </span>
                        <span className="text-xs text-gray-500 flex items-center">
                          <Eye className="w-3 h-3 mr-1" />
                          {listing.views}
                        </span>
                        <span className="text-xs text-gray-500 flex items-center">
                          <Heart className="w-3 h-3 mr-1" />
                          {listing.favorites}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {new Date(listing.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No listings yet</h3>
                <p className="text-gray-600 mb-4">Start selling by creating your first listing</p>
                <Link
                  href="/create"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Create Your First Listing
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Link
            href="/create"
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all group"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <Plus className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold">Create Listing</h3>
                <p className="text-sm opacity-90">Sell something new</p>
              </div>
            </div>
          </Link>

          <Link
            href="/messages"
            className="bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all group"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <MessageCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">Messages</h3>
                <p className="text-sm text-gray-600">3 unread</p>
              </div>
            </div>
          </Link>

          <Link
            href="/favorites"
            className="bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all group"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <Heart className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">Favorites</h3>
                <p className="text-sm text-gray-600">Saved items</p>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </DashboardLayout>
  )
}
