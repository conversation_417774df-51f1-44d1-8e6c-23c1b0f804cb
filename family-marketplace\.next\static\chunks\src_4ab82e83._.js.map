{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport Image from 'next/image'\nimport { UploadResult } from '@/types'\n\n// Define interfaces locally to avoid import issues\ninterface AIAnalysisResult {\n  category: string\n  subcategory?: string\n  title: string\n  description: string\n  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'\n  brand?: string\n  model?: string\n  tags: string[]\n  confidence: number\n}\n\ninterface ImageUploadProps {\n  onUpload: (result: UploadResult) => void\n  onAnalysis?: (analysis: AIAnalysisResult) => void\n  maxFiles?: number\n  className?: string\n}\n\nexport default function ImageUpload({ \n  onUpload, \n  onAnalysis, \n  maxFiles = 5, \n  className = '' \n}: ImageUploadProps) {\n  const [uploading, setUploading] = useState(false)\n  const [analyzing, setAnalyzing] = useState(false)\n  const [uploadedImages, setUploadedImages] = useState<UploadResult[]>([])\n  const [error, setError] = useState<string | null>(null)\n\n  const uploadImage = async (file: File): Promise<UploadResult> => {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const response = await fetch('/api/upload', {\n      method: 'POST',\n      body: formData\n    })\n\n    const result = await response.json()\n    \n    if (!result.success) {\n      throw new Error(result.error || 'Upload failed')\n    }\n\n    return result.data\n  }\n\n  const analyzeImage = async (imageUrl: string): Promise<AIAnalysisResult> => {\n    const response = await fetch('/api/ai/analyze', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({ imageUrl, type: 'image' })\n    })\n\n    const result = await response.json()\n    \n    if (!result.success) {\n      throw new Error(result.error || 'Analysis failed')\n    }\n\n    return result.data\n  }\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (uploadedImages.length + acceptedFiles.length > maxFiles) {\n      setError(`Maximum ${maxFiles} images allowed`)\n      return\n    }\n\n    setUploading(true)\n    setError(null)\n\n    try {\n      const uploadPromises = acceptedFiles.map(uploadImage)\n      const uploadResults = await Promise.all(uploadPromises)\n      \n      setUploadedImages(prev => [...prev, ...uploadResults])\n      \n      // Notify parent component\n      uploadResults.forEach(result => onUpload(result))\n\n      // Analyze the first uploaded image if analysis callback is provided\n      if (onAnalysis && uploadResults.length > 0) {\n        setAnalyzing(true)\n        try {\n          const analysis = await analyzeImage(uploadResults[0].url)\n          onAnalysis(analysis)\n        } catch (analysisError) {\n          console.error('Analysis failed:', analysisError)\n          // Don't show error for analysis failure - it's optional\n        } finally {\n          setAnalyzing(false)\n        }\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Upload failed')\n    } finally {\n      setUploading(false)\n    }\n  }, [uploadedImages.length, maxFiles, onUpload, onAnalysis])\n\n  const removeImage = async (index: number) => {\n    const image = uploadedImages[index]\n    \n    try {\n      // Delete from server\n      await fetch(`/api/upload?filename=${image.filename}`, {\n        method: 'DELETE'\n      })\n      \n      // Remove from state\n      setUploadedImages(prev => prev.filter((_, i) => i !== index))\n    } catch (error) {\n      console.error('Failed to delete image:', error)\n    }\n  }\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.webp']\n    },\n    maxFiles: maxFiles - uploadedImages.length,\n    disabled: uploading || uploadedImages.length >= maxFiles\n  })\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Upload Area */}\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}\n          ${uploading ? 'opacity-50 cursor-not-allowed' : ''}\n          ${uploadedImages.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : ''}\n        `}\n      >\n        <input {...getInputProps()} />\n        \n        {uploading ? (\n          <div className=\"space-y-2\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"></div>\n            <p className=\"text-gray-600\">Uploading images...</p>\n          </div>\n        ) : analyzing ? (\n          <div className=\"space-y-2\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mx-auto\"></div>\n            </div>\n            <p className=\"text-gray-600\">🤖 AI is analyzing your image...</p>\n            <p className=\"text-sm text-gray-500\">This will help suggest category, title, and description</p>\n          </div>\n        ) : uploadedImages.length >= maxFiles ? (\n          <p className=\"text-gray-500\">Maximum {maxFiles} images uploaded</p>\n        ) : (\n          <div className=\"space-y-2\">\n            <div className=\"text-4xl\">📸</div>\n            <p className=\"text-lg font-medium\">\n              {isDragActive ? 'Drop images here' : 'Upload product images'}\n            </p>\n            <p className=\"text-gray-500\">\n              Drag & drop or click to select ({uploadedImages.length}/{maxFiles})\n            </p>\n            <p className=\"text-sm text-blue-600\">\n              ✨ AI will automatically analyze your images to suggest details\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n          <p className=\"text-red-700 text-sm\">{error}</p>\n        </div>\n      )}\n\n      {/* Uploaded Images */}\n      {uploadedImages.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\">\n          {uploadedImages.map((image, index) => (\n            <div key={image.filename} className=\"relative group\">\n              <div className=\"aspect-square relative rounded-lg overflow-hidden border border-gray-200\">\n                <Image\n                  src={image.url}\n                  alt={`Upload ${index + 1}`}\n                  fill\n                  className=\"object-cover\"\n                />\n                <button\n                  onClick={() => removeImage(index)}\n                  className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity\"\n                >\n                  ×\n                </button>\n              </div>\n              <p className=\"text-xs text-gray-500 mt-1 truncate\">\n                {(image.size / 1024).toFixed(1)}KB\n              </p>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AA2Be,SAAS,YAAY,EAClC,QAAQ,EACR,UAAU,EACV,WAAW,CAAC,EACZ,YAAY,EAAE,EACG;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,MAAM,eAAe;YAC1C,QAAQ;YACR,MAAM;QACR;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,WAAW,MAAM,MAAM,mBAAmB;YAC9C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAU,MAAM;YAAQ;QACjD;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,OAAO;YAChC,IAAI,eAAe,MAAM,GAAG,cAAc,MAAM,GAAG,UAAU;gBAC3D,SAAS,CAAC,QAAQ,EAAE,SAAS,eAAe,CAAC;gBAC7C;YACF;YAEA,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,iBAAiB,cAAc,GAAG,CAAC;gBACzC,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CAAC;gBAExC;uDAAkB,CAAA,OAAQ;+BAAI;+BAAS;yBAAc;;gBAErD,0BAA0B;gBAC1B,cAAc,OAAO;uDAAC,CAAA,SAAU,SAAS;;gBAEzC,oEAAoE;gBACpE,IAAI,cAAc,cAAc,MAAM,GAAG,GAAG;oBAC1C,aAAa;oBACb,IAAI;wBACF,MAAM,WAAW,MAAM,aAAa,aAAa,CAAC,EAAE,CAAC,GAAG;wBACxD,WAAW;oBACb,EAAE,OAAO,eAAe;wBACtB,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,wDAAwD;oBAC1D,SAAU;wBACR,aAAa;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD,SAAU;gBACR,aAAa;YACf;QACF;0CAAG;QAAC,eAAe,MAAM;QAAE;QAAU;QAAU;KAAW;IAE1D,MAAM,cAAc,OAAO;QACzB,MAAM,QAAQ,cAAc,CAAC,MAAM;QAEnC,IAAI;YACF,qBAAqB;YACrB,MAAM,MAAM,CAAC,qBAAqB,EAAE,MAAM,QAAQ,EAAE,EAAE;gBACpD,QAAQ;YACV;YAEA,oBAAoB;YACpB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;aAAQ;QAC/C;QACA,UAAU,WAAW,eAAe,MAAM;QAC1C,UAAU,aAAa,eAAe,MAAM,IAAI;IAClD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eAAe,+BAA+B,wCAAwC;UACxF,EAAE,YAAY,kCAAkC,GAAG;UACnD,EAAE,eAAe,MAAM,IAAI,WAAW,kCAAkC,GAAG;QAC7E,CAAC;;kCAED,6LAAC;wBAAO,GAAG,eAAe;;;;;;oBAEzB,0BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,0BACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;+BAErC,eAAe,MAAM,IAAI,yBAC3B,6LAAC;wBAAE,WAAU;;4BAAgB;4BAAS;4BAAS;;;;;;6CAE/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAW;;;;;;0CAC1B,6LAAC;gCAAE,WAAU;0CACV,eAAe,qBAAqB;;;;;;0CAEvC,6LAAC;gCAAE,WAAU;;oCAAgB;oCACM,eAAe,MAAM;oCAAC;oCAAE;oCAAS;;;;;;;0CAEpE,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAQ1C,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;YAKxC,eAAe,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;wBAAyB,WAAU;;0CAClC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,MAAM,GAAG;wCACd,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG;wCAC1B,IAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDACX;;;;;;;;;;;;0CAIH,6LAAC;gCAAE,WAAU;;oCACV,CAAC,MAAM,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;;uBAhB1B,MAAM,QAAQ;;;;;;;;;;;;;;;;AAwBpC;GA9LwB;;QAqGgC,2KAAA,CAAA,cAAW;;;KArG3C", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/currency.ts"], "sourcesContent": ["// South African currency utilities\nexport const formatPrice = (amount: number): string => {\n  return new Intl.NumberFormat('en-ZA', {\n    style: 'currency',\n    currency: 'ZAR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport const formatPriceShort = (amount: number): string => {\n  if (amount >= 1000000) {\n    return `R${(amount / 1000000).toFixed(1)}M`\n  } else if (amount >= 1000) {\n    return `R${(amount / 1000).toFixed(1)}K`\n  }\n  return formatPrice(amount)\n}\n\nexport const parsePrice = (priceString: string): number => {\n  // Remove currency symbols and parse\n  const cleaned = priceString.replace(/[R\\s,]/g, '')\n  return parseFloat(cleaned) || 0\n}\n\n// South African provinces for location selection\nexport const southAfricanProvinces = [\n  'Eastern Cape',\n  'Free State',\n  'Gauteng',\n  'KwaZulu-Natal',\n  'Limpopo',\n  'Mpumalanga',\n  'Northern Cape',\n  'North West',\n  'Western Cape'\n]\n\n// Major South African cities\nexport const southAfricanCities = {\n  'Gauteng': ['Johannesburg', 'Pretoria', 'Sandton', 'Randburg', 'Roodepoort', 'Germiston', 'Benoni'],\n  'Western Cape': ['Cape Town', 'Stellenbosch', 'Paarl', 'George', 'Worcester', 'Hermanus'],\n  'KwaZulu-Natal': ['Durban', 'Pietermaritzburg', 'Newcastle', 'Ladysmith', 'Richards Bay'],\n  'Eastern Cape': ['Port Elizabeth', 'East London', 'Uitenhage', 'King Williams Town', 'Grahamstown'],\n  'Free State': ['Bloemfontein', 'Welkom', 'Kroonstad', 'Bethlehem', 'Sasolburg'],\n  'Limpopo': ['Polokwane', 'Tzaneen', 'Mokopane', 'Thohoyandou', 'Giyani'],\n  'Mpumalanga': ['Nelspruit', 'Witbank', 'Secunda', 'Standerton', 'Ermelo'],\n  'Northern Cape': ['Kimberley', 'Upington', 'Springbok', 'De Aar', 'Kuruman'],\n  'North West': ['Mahikeng', 'Rustenburg', 'Klerksdorp', 'Potchefstroom', 'Brits']\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;;;AAC5B,MAAM,cAAc,CAAC;IAC1B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,UAAU,SAAS;QACrB,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7C,OAAO,IAAI,UAAU,MAAM;QACzB,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C;IACA,OAAO,YAAY;AACrB;AAEO,MAAM,aAAa,CAAC;IACzB,oCAAoC;IACpC,MAAM,UAAU,YAAY,OAAO,CAAC,WAAW;IAC/C,OAAO,WAAW,YAAY;AAChC;AAGO,MAAM,wBAAwB;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAqB;IAChC,WAAW;QAAC;QAAgB;QAAY;QAAW;QAAY;QAAc;QAAa;KAAS;IACnG,gBAAgB;QAAC;QAAa;QAAgB;QAAS;QAAU;QAAa;KAAW;IACzF,iBAAiB;QAAC;QAAU;QAAoB;QAAa;QAAa;KAAe;IACzF,gBAAgB;QAAC;QAAkB;QAAe;QAAa;QAAsB;KAAc;IACnG,cAAc;QAAC;QAAgB;QAAU;QAAa;QAAa;KAAY;IAC/E,WAAW;QAAC;QAAa;QAAW;QAAY;QAAe;KAAS;IACxE,cAAc;QAAC;QAAa;QAAW;QAAW;QAAc;KAAS;IACzE,iBAAiB;QAAC;QAAa;QAAY;QAAa;QAAU;KAAU;IAC5E,cAAc;QAAC;QAAY;QAAc;QAAc;QAAiB;KAAQ;AAClF", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/CreateListingForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport ImageUpload from './ImageUpload'\nimport { UploadResult } from '@/types'\nimport { formatPrice, southAfricanProvinces, southAfricanCities } from '@/lib/currency'\n\n// Local interfaces to avoid import issues\ninterface AIAnalysisResult {\n  category: string\n  subcategory?: string\n  title: string\n  description: string\n  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'\n  brand?: string\n  model?: string\n  tags: string[]\n  confidence: number\n}\n\ninterface PriceSuggestion {\n  suggestedPrice: number\n  priceRange: { min: number; max: number }\n  confidence: number\n  reasoning: string\n}\n\ninterface FormData {\n  title: string\n  description: string\n  price: number\n  condition: string\n  category: string\n  subcategory: string\n  brand: string\n  model: string\n  location: string\n  images: UploadResult[]\n}\n\nconst CATEGORIES = [\n  'Electronics',\n  'Furniture',\n  'Clothing',\n  'Books',\n  'Sports & Outdoors',\n  'Home & Garden',\n  'Toys & Games',\n  'Automotive',\n  'Health & Beauty',\n  'Other'\n]\n\nconst CONDITIONS = [\n  { value: 'NEW', label: 'New' },\n  { value: 'LIKE_NEW', label: 'Like New' },\n  { value: 'GOOD', label: 'Good' },\n  { value: 'FAIR', label: 'Fair' },\n  { value: 'POOR', label: 'Poor' }\n]\n\nexport default function CreateListingForm() {\n  const { data: session } = useSession()\n\n  const [formData, setFormData] = useState<FormData>({\n    title: '',\n    description: '',\n    price: 0,\n    condition: 'GOOD',\n    category: '',\n    subcategory: '',\n    brand: '',\n    model: '',\n    location: '',\n    images: []\n  })\n\n  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysisResult | null>(null)\n  const [priceSuggestion, setPriceSuggestion] = useState<PriceSuggestion | null>(null)\n  const [isLoadingPrice, setIsLoadingPrice] = useState(false)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [showAiSuggestions, setShowAiSuggestions] = useState(true)\n\n  // Handle AI analysis results\n  const handleAiAnalysis = (analysis: AIAnalysisResult) => {\n    setAiAnalysis(analysis)\n    \n    // Auto-fill form with AI suggestions if user wants\n    if (showAiSuggestions) {\n      setFormData(prev => ({\n        ...prev,\n        title: analysis.title || prev.title,\n        description: analysis.description || prev.description,\n        category: analysis.category || prev.category,\n        subcategory: analysis.subcategory || prev.subcategory,\n        brand: analysis.brand || prev.brand,\n        model: analysis.model || prev.model,\n        condition: analysis.condition || prev.condition\n      }))\n    }\n  }\n\n  // Get price suggestion when enough data is available\n  useEffect(() => {\n    const getPriceSuggestion = async () => {\n      if (!formData.category || !formData.condition || !formData.description) {\n        return\n      }\n\n      setIsLoadingPrice(true)\n      try {\n        const response = await fetch('/api/ai/price', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({\n            category: formData.category,\n            subcategory: formData.subcategory,\n            brand: formData.brand,\n            model: formData.model,\n            condition: formData.condition,\n            description: formData.description\n          })\n        })\n\n        const result = await response.json()\n        if (result.success) {\n          setPriceSuggestion(result.data)\n          \n          // Auto-fill price if user hasn't set one\n          if (formData.price === 0 && showAiSuggestions) {\n            setFormData(prev => ({\n              ...prev,\n              price: result.data.suggestedPrice\n            }))\n          }\n        }\n      } catch (error) {\n        console.error('Price suggestion failed:', error)\n      } finally {\n        setIsLoadingPrice(false)\n      }\n    }\n\n    // Debounce the price suggestion\n    const timer = setTimeout(getPriceSuggestion, 1000)\n    return () => clearTimeout(timer)\n  }, [formData.category, formData.condition, formData.description, formData.brand, formData.model, formData.subcategory])\n\n  const handleInputChange = (field: keyof FormData, value: string | number) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const handleImageUpload = (result: UploadResult) => {\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, result]\n    }))\n  }\n\n  const applySuggestion = (field: keyof FormData, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n\n    try {\n      const listingData = {\n        title: formData.title,\n        description: formData.description,\n        price: formData.price,\n        condition: formData.condition,\n        category: formData.category,\n        subcategory: formData.subcategory,\n        brand: formData.brand,\n        model: formData.model,\n        location: formData.location,\n        images: formData.images,\n        // Include AI data if available\n        aiGeneratedTitle: aiAnalysis?.title,\n        aiGeneratedDescription: aiAnalysis?.description,\n        aiSuggestedPrice: priceSuggestion?.suggestedPrice,\n        aiConfidenceScore: aiAnalysis?.confidence,\n        aiTags: aiAnalysis?.tags\n      }\n\n      const response = await fetch('/api/listings', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(listingData)\n      })\n\n      const result = await response.json()\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Failed to create listing')\n      }\n\n      alert('Listing created successfully! 🎉')\n\n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        price: 0,\n        condition: 'GOOD',\n        category: '',\n        subcategory: '',\n        brand: '',\n        model: '',\n        location: '',\n        images: []\n      })\n      setAiAnalysis(null)\n      setPriceSuggestion(null)\n    } catch (error) {\n      console.error('Submission failed:', error)\n      alert(error instanceof Error ? error.message : 'Failed to create listing. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Create New Listing\n        </h1>\n        <p className=\"text-gray-600\">\n          🤖 Upload photos and let AI help you create the perfect listing\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Image Upload Section */}\n        <div className=\"space-y-4\">\n          <h2 className=\"text-xl font-semibold\">Product Images</h2>\n          <ImageUpload\n            onUpload={handleImageUpload}\n            onAnalysis={handleAiAnalysis}\n            maxFiles={5}\n          />\n        </div>\n\n        {/* AI Analysis Results */}\n        {aiAnalysis && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"text-2xl mr-2\">🤖</div>\n              <h3 className=\"text-lg font-semibold text-purple-800\">\n                AI Analysis Results\n              </h3>\n              <span className=\"ml-auto text-sm text-purple-600\">\n                Confidence: {Math.round(aiAnalysis.confidence * 100)}%\n              </span>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n              <div>\n                <strong>Suggested Title:</strong>\n                <p className=\"text-gray-700\">{aiAnalysis.title}</p>\n                {aiAnalysis.title !== formData.title && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('title', aiAnalysis.title)}\n                    className=\"text-blue-600 hover:underline text-xs\"\n                  >\n                    Apply suggestion\n                  </button>\n                )}\n              </div>\n              \n              <div>\n                <strong>Category:</strong>\n                <p className=\"text-gray-700\">{aiAnalysis.category}</p>\n                {aiAnalysis.category !== formData.category && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('category', aiAnalysis.category)}\n                    className=\"text-blue-600 hover:underline text-xs\"\n                  >\n                    Apply suggestion\n                  </button>\n                )}\n              </div>\n              \n              {aiAnalysis.brand && (\n                <div>\n                  <strong>Brand:</strong>\n                  <p className=\"text-gray-700\">{aiAnalysis.brand}</p>\n                  {aiAnalysis.brand !== formData.brand && (\n                    <button\n                      type=\"button\"\n                      onClick={() => applySuggestion('brand', aiAnalysis.brand)}\n                      className=\"text-blue-600 hover:underline text-xs\"\n                    >\n                      Apply suggestion\n                    </button>\n                  )}\n                </div>\n              )}\n              \n              <div>\n                <strong>Condition:</strong>\n                <p className=\"text-gray-700\">{aiAnalysis.condition}</p>\n                {aiAnalysis.condition !== formData.condition && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('condition', aiAnalysis.condition)}\n                    className=\"text-blue-600 hover:underline text-xs\"\n                  >\n                    Apply suggestion\n                  </button>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"mt-4\">\n              <strong>Suggested Description:</strong>\n              <p className=\"text-gray-700 text-sm mt-1\">{aiAnalysis.description}</p>\n              {aiAnalysis.description !== formData.description && (\n                <button\n                  type=\"button\"\n                  onClick={() => applySuggestion('description', aiAnalysis.description)}\n                  className=\"text-blue-600 hover:underline text-xs mt-1\"\n                >\n                  Apply suggestion\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Form Fields */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Title */}\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.title}\n              onChange={(e) => handleInputChange('title', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Enter a descriptive title for your item\"\n            />\n          </div>\n\n          {/* Category */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Category *\n            </label>\n            <select\n              required\n              value={formData.category}\n              onChange={(e) => handleInputChange('category', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Select a category</option>\n              {CATEGORIES.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Condition */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Condition *\n            </label>\n            <select\n              required\n              value={formData.condition}\n              onChange={(e) => handleInputChange('condition', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {CONDITIONS.map(condition => (\n                <option key={condition.value} value={condition.value}>\n                  {condition.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Brand */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Brand\n            </label>\n            <input\n              type=\"text\"\n              value={formData.brand}\n              onChange={(e) => handleInputChange('brand', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Brand name\"\n            />\n          </div>\n\n          {/* Model */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Model\n            </label>\n            <input\n              type=\"text\"\n              value={formData.model}\n              onChange={(e) => handleInputChange('model', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Model name/number\"\n            />\n          </div>\n        </div>\n\n        {/* Price Section */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <label className=\"block text-sm font-medium text-gray-700\">\n              Price * (ZAR)\n            </label>\n            {isLoadingPrice && (\n              <span className=\"text-sm text-blue-600\">🤖 Getting price suggestion...</span>\n            )}\n          </div>\n          \n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-500 sm:text-sm\">R</span>\n            </div>\n            <input\n              type=\"number\"\n              required\n              min=\"0\"\n              step=\"1\"\n              value={formData.price}\n              onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}\n              className=\"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"0\"\n            />\n          </div>\n          {formData.price > 0 && (\n            <p className=\"text-sm text-gray-500 mt-1\">\n              Display price: {formatPrice(formData.price)}\n            </p>\n          )}\n\n          {/* Price Suggestion */}\n          {priceSuggestion && (\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-green-600 font-medium\">💡 AI Price Suggestion</span>\n                <span className=\"ml-auto text-sm text-green-600\">\n                  Confidence: {Math.round(priceSuggestion.confidence * 100)}%\n                </span>\n              </div>\n              <div className=\"text-sm text-green-700\">\n                <p><strong>Suggested Price:</strong> {formatPrice(priceSuggestion.suggestedPrice)}</p>\n                <p><strong>Price Range:</strong> {formatPrice(priceSuggestion.priceRange.min)} - {formatPrice(priceSuggestion.priceRange.max)}</p>\n                <p className=\"mt-2\"><strong>Reasoning:</strong> {priceSuggestion.reasoning}</p>\n                {priceSuggestion.suggestedPrice !== formData.price && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('price', priceSuggestion.suggestedPrice)}\n                    className=\"text-green-600 hover:underline text-sm mt-2\"\n                  >\n                    Apply suggested price\n                  </button>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Description */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Description *\n          </label>\n          <textarea\n            required\n            rows={6}\n            value={formData.description}\n            onChange={(e) => handleInputChange('description', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Describe your item in detail...\"\n          />\n        </div>\n\n        {/* Location */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Location *\n          </label>\n          <input\n            type=\"text\"\n            required\n            value={formData.location}\n            onChange={(e) => handleInputChange('location', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"e.g. Cape Town, Western Cape\"\n            list=\"sa-cities\"\n          />\n          <datalist id=\"sa-cities\">\n            {Object.entries(southAfricanCities).map(([province, cities]) =>\n              cities.map(city => (\n                <option key={`${city}-${province}`} value={`${city}, ${province}`} />\n              ))\n            )}\n          </datalist>\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <button\n            type=\"button\"\n            className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n          >\n            Save as Draft\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isSubmitting || formData.images.length === 0}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSubmitting ? 'Creating Listing...' : 'Create Listing'}\n          </button>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAyCA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;IAAM;IAC7B;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAQ,OAAO;IAAO;CAChC;AAEc,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,OAAO;QACP,aAAa;QACb,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ,EAAE;IACZ;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,cAAc;QAEd,mDAAmD;QACnD,IAAI,mBAAmB;YACrB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,OAAO,SAAS,KAAK,IAAI,KAAK,KAAK;oBACnC,aAAa,SAAS,WAAW,IAAI,KAAK,WAAW;oBACrD,UAAU,SAAS,QAAQ,IAAI,KAAK,QAAQ;oBAC5C,aAAa,SAAS,WAAW,IAAI,KAAK,WAAW;oBACrD,OAAO,SAAS,KAAK,IAAI,KAAK,KAAK;oBACnC,OAAO,SAAS,KAAK,IAAI,KAAK,KAAK;oBACnC,WAAW,SAAS,SAAS,IAAI,KAAK,SAAS;gBACjD,CAAC;QACH;IACF;IAEA,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB;oBACzB,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,WAAW,EAAE;wBACtE;oBACF;oBAEA,kBAAkB;oBAClB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;4BAC5C,QAAQ;4BACR,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,MAAM,KAAK,SAAS,CAAC;gCACnB,UAAU,SAAS,QAAQ;gCAC3B,aAAa,SAAS,WAAW;gCACjC,OAAO,SAAS,KAAK;gCACrB,OAAO,SAAS,KAAK;gCACrB,WAAW,SAAS,SAAS;gCAC7B,aAAa,SAAS,WAAW;4BACnC;wBACF;wBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;wBAClC,IAAI,OAAO,OAAO,EAAE;4BAClB,mBAAmB,OAAO,IAAI;4BAE9B,yCAAyC;4BACzC,IAAI,SAAS,KAAK,KAAK,KAAK,mBAAmB;gCAC7C;sFAAY,CAAA,OAAQ,CAAC;4CACnB,GAAG,IAAI;4CACP,OAAO,OAAO,IAAI,CAAC,cAAc;wCACnC,CAAC;;4BACH;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,kBAAkB;oBACpB;gBACF;;YAEA,gCAAgC;YAChC,MAAM,QAAQ,WAAW,oBAAoB;YAC7C;+CAAO,IAAM,aAAa;;QAC5B;sCAAG;QAAC,SAAS,QAAQ;QAAE,SAAS,SAAS;QAAE,SAAS,WAAW;QAAE,SAAS,KAAK;QAAE,SAAS,KAAK;QAAE,SAAS,WAAW;KAAC;IAEtH,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;uBAAI,KAAK,MAAM;oBAAE;iBAAO;YAClC,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC,OAAuB;QAC9C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,aAAa,SAAS,WAAW;gBACjC,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,QAAQ,SAAS,MAAM;gBACvB,+BAA+B;gBAC/B,kBAAkB,YAAY;gBAC9B,wBAAwB,YAAY;gBACpC,kBAAkB,iBAAiB;gBACnC,mBAAmB,YAAY;gBAC/B,QAAQ,YAAY;YACtB;YAEA,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,MAAM;YAEN,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,QAAQ,EAAE;YACZ;YACA,cAAc;YACd,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACjD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAK/B,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC,oIAAA,CAAA,UAAW;gCACV,UAAU;gCACV,YAAY;gCACZ,UAAU;;;;;;;;;;;;oBAKb,4BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAK,WAAU;;4CAAkC;4CACnC,KAAK,KAAK,CAAC,WAAW,UAAU,GAAG;4CAAK;;;;;;;;;;;;;0CAIzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;0DAAO;;;;;;0DACR,6LAAC;gDAAE,WAAU;0DAAiB,WAAW,KAAK;;;;;;4CAC7C,WAAW,KAAK,KAAK,SAAS,KAAK,kBAClC,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,SAAS,WAAW,KAAK;gDACxD,WAAU;0DACX;;;;;;;;;;;;kDAML,6LAAC;;0DACC,6LAAC;0DAAO;;;;;;0DACR,6LAAC;gDAAE,WAAU;0DAAiB,WAAW,QAAQ;;;;;;4CAChD,WAAW,QAAQ,KAAK,SAAS,QAAQ,kBACxC,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,YAAY,WAAW,QAAQ;gDAC9D,WAAU;0DACX;;;;;;;;;;;;oCAMJ,WAAW,KAAK,kBACf,6LAAC;;0DACC,6LAAC;0DAAO;;;;;;0DACR,6LAAC;gDAAE,WAAU;0DAAiB,WAAW,KAAK;;;;;;4CAC7C,WAAW,KAAK,KAAK,SAAS,KAAK,kBAClC,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,SAAS,WAAW,KAAK;gDACxD,WAAU;0DACX;;;;;;;;;;;;kDAOP,6LAAC;;0DACC,6LAAC;0DAAO;;;;;;0DACR,6LAAC;gDAAE,WAAU;0DAAiB,WAAW,SAAS;;;;;;4CACjD,WAAW,SAAS,KAAK,SAAS,SAAS,kBAC1C,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,aAAa,WAAW,SAAS;gDAChE,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAOP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAO;;;;;;kDACR,6LAAC;wCAAE,WAAU;kDAA8B,WAAW,WAAW;;;;;;oCAChE,WAAW,WAAW,KAAK,SAAS,WAAW,kBAC9C,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB,eAAe,WAAW,WAAW;wCACpE,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAST,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,QAAQ;wCACR,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oDAAsB,OAAO;8DAAW;mDAA5B;;;;;;;;;;;;;;;;;0CAMnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,QAAQ;wCACR,OAAO,SAAS,SAAS;wCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC9D,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,0BACd,6LAAC;gDAA6B,OAAO,UAAU,KAAK;0DACjD,UAAU,KAAK;+CADL,UAAU,KAAK;;;;;;;;;;;;;;;;0CAQlC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA0C;;;;;;oCAG1D,gCACC,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA2B;;;;;;;;;;;kDAE7C,6LAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,KAAI;wCACJ,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wCAC1E,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,SAAS,KAAK,GAAG,mBAChB,6LAAC;gCAAE,WAAU;;oCAA6B;oCACxB,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,KAAK;;;;;;;4BAK7C,iCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,6LAAC;gDAAK,WAAU;;oDAAiC;oDAClC,KAAK,KAAK,CAAC,gBAAgB,UAAU,GAAG;oDAAK;;;;;;;;;;;;;kDAG9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAyB;oDAAE,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,cAAc;;;;;;;0DAChF,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAqB;oDAAE,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,UAAU,CAAC,GAAG;oDAAE;oDAAI,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,UAAU,CAAC,GAAG;;;;;;;0DAC5H,6LAAC;gDAAE,WAAU;;kEAAO,6LAAC;kEAAO;;;;;;oDAAmB;oDAAE,gBAAgB,SAAS;;;;;;;4CACzE,gBAAgB,cAAc,KAAK,SAAS,KAAK,kBAChD,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,SAAS,gBAAgB,cAAc;gDACtE,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,QAAQ;gCACR,MAAM;gCACN,OAAO,SAAS,WAAW;gCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gCAChE,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,QAAQ;gCACR,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC7D,WAAU;gCACV,aAAY;gCACZ,MAAK;;;;;;0CAEP,6LAAC;gCAAS,IAAG;0CACV,OAAO,OAAO,CAAC,yHAAA,CAAA,qBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,GACzD,OAAO,GAAG,CAAC,CAAA,qBACT,6LAAC;4CAAmC,OAAO,GAAG,KAAK,EAAE,EAAE,UAAU;2CAApD,GAAG,KAAK,CAAC,EAAE,UAAU;;;;;;;;;;;;;;;;kCAO1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU,gBAAgB,SAAS,MAAM,CAAC,MAAM,KAAK;gCACrD,WAAU;0CAET,eAAe,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAMpD;GAjewB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport CreateListingForm from '@/components/CreateListingForm'\nimport Link from 'next/link'\n\nexport default function CreateListingPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/login?message=Please sign in to create a listing&redirect=/create')\n    }\n  }, [status, router])\n\n  // Show loading state while checking authentication\n  if (status === 'loading') {\n    return (\n      <div className=\"bg-gray-50 py-8\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"bg-white rounded-lg shadow-lg p-8\">\n            <div className=\"text-center\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Checking authentication...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  // Show login prompt if not authenticated\n  if (status === 'unauthenticated') {\n    return (\n      <div className=\"bg-gray-50 py-8\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"bg-white rounded-lg shadow-lg p-8 text-center\">\n            <div className=\"text-6xl mb-6\">🔒</div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Sign In Required\n            </h1>\n            <p className=\"text-gray-600 mb-8\">\n              You need to be signed in to create listings. This helps us associate your listings with your account and provide the best AI-powered experience.\n            </p>\n\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto\">\n                <Link\n                  href=\"/login?redirect=/create\"\n                  className=\"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all\"\n                >\n                  Sign In\n                </Link>\n\n                <div className=\"relative group\">\n                  <button className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all\">\n                    Create Account\n                  </button>\n                  <div className=\"absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10\">\n                    <div className=\"py-2\">\n                      <Link\n                        href=\"/register?type=private&redirect=/create\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50\"\n                      >\n                        <span className=\"mr-2\">👤</span>\n                        Private Seller\n                      </Link>\n                      <Link\n                        href=\"/register?type=vendor&redirect=/create\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-purple-50\"\n                      >\n                        <span className=\"mr-2\">🏪</span>\n                        Business Vendor\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-8 p-4 bg-blue-50 rounded-lg\">\n                <h3 className=\"font-semibold text-blue-800 mb-2\">Why sign in?</h3>\n                <ul className=\"text-sm text-blue-700 space-y-1\">\n                  <li>• AI personalizes suggestions based on your account type</li>\n                  <li>• Manage all your listings in one place</li>\n                  <li>• Buyers can contact you securely</li>\n                  <li>• Track views and engagement on your listings</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  // User is authenticated, show the create listing form\n  return (\n    <div className=\"bg-gray-50 py-8\">\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* User Info Header */}\n        <div className=\"bg-white rounded-lg shadow-sm p-4 mb-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-semibold\">\n                {session.user.firstName ? session.user.firstName[0] : session.user.username[0].toUpperCase()}\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  Creating listing as {session.user.firstName || session.user.username}\n                </p>\n                <div className=\"flex items-center\">\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                    session.user.userType === 'VENDOR'\n                      ? 'bg-purple-100 text-purple-800'\n                      : 'bg-blue-100 text-blue-800'\n                  }`}>\n                    {session.user.userType === 'VENDOR' ? '🏪 Vendor Account' : '👤 Private Seller'}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {session.user.userType === 'VENDOR' && session.user.vendorProfile && (\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  {session.user.vendorProfile.businessName}\n                </p>\n                <p className=\"text-xs text-gray-500\">Business Account</p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        <CreateListingForm />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,WAAW,mBAAmB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;sCAAG;QAAC;QAAQ;KAAO;IAEnB,mDAAmD;IACnD,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzC;IAEA,yCAAyC;IACzC,IAAI,WAAW,mBAAmB;QAChC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAIlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAID,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;8DAA2J;;;;;;8DAG7K,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,6LAAC;wEAAK,WAAU;kFAAO;;;;;;oEAAS;;;;;;;0EAGlC,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,6LAAC;wEAAK,WAAU;kFAAO;;;;;;oEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpB;IAEA,sDAAsD;IACtD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI,CAAC,SAAS,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW;;;;;;kDAE5F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAoC;oDAC1B,QAAQ,IAAI,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC,QAAQ;;;;;;;0DAEtE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAW,CAAC,oEAAoE,EACpF,QAAQ,IAAI,CAAC,QAAQ,KAAK,WACtB,kCACA,6BACJ;8DACC,QAAQ,IAAI,CAAC,QAAQ,KAAK,WAAW,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;4BAMnE,QAAQ,IAAI,CAAC,QAAQ,KAAK,YAAY,QAAQ,IAAI,CAAC,aAAa,kBAC/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,QAAQ,IAAI,CAAC,aAAa,CAAC,YAAY;;;;;;kDAE1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAM7C,6LAAC,0IAAA,CAAA,UAAiB;;;;;;;;;;;;;;;;AAI1B;GApIwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}