// AI-Powered Multi-Vendor Marketplace Schema
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User Management
model User {
  id         String   @id @default(cuid())
  email      String   @unique
  username   String   @unique
  password   String
  firstName  String?
  lastName   String?
  phone      String?
  avatar     String?
  userType   UserType @default(PRIVATE)
  isVerified Boolean  @default(false)
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relationships
  listings     Listing[]
  messages     Message[]
  reviews      Review[]
  reviewsGiven Review[]      @relation("ReviewGiver")
  favorites    Favorite[]
  searches     SavedSearch[]

  // Vendor specific
  vendorProfile VendorProfile?

  @@map("users")
}

model VendorProfile {
  id           String  @id @default(cuid())
  userId       String  @unique
  businessName String
  description  String?
  website      String?
  logo         String?
  verified     Boolean @default(false)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("vendor_profiles")
}

// Listing Management
model Listing {
  id          String        @id @default(cuid())
  title       String
  description String
  price       Float
  condition   Condition
  status      ListingStatus @default(ACTIVE)
  category    String
  subcategory String?
  brand       String?
  model       String?

  // AI Generated Fields
  aiGeneratedTitle       String?
  aiGeneratedDescription String?
  aiSuggestedPrice       Float?
  aiConfidenceScore      Float?
  aiTags                 Json? // JSON array of AI-extracted tags

  // Location
  location  String
  latitude  Float?
  longitude Float?

  // Metadata
  views     Int      @default(0)
  featured  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  userId    String
  user      User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  images    ListingImage[]
  messages  Message[]
  reviews   Review[]
  favorites Favorite[]

  @@map("listings")
}

model ListingImage {
  id        String  @id @default(cuid())
  listingId String
  url       String
  altText   String?
  isPrimary Boolean @default(false)
  order     Int     @default(0)

  // AI Analysis Results
  aiAnalysis Json? // Store AI image analysis results

  listing Listing @relation(fields: [listingId], references: [id], onDelete: Cascade)

  @@map("listing_images")
}

// Communication
model Message {
  id          String      @id @default(cuid())
  content     String
  isRead      Boolean     @default(false)
  messageType MessageType @default(USER)

  // AI Generated Response
  isAiGenerated Boolean @default(false)

  createdAt DateTime @default(now())

  // Relationships
  senderId  String
  listingId String
  sender    User    @relation(fields: [senderId], references: [id], onDelete: Cascade)
  listing   Listing @relation(fields: [listingId], references: [id], onDelete: Cascade)

  @@map("messages")
}

// Reviews and Trust
model Review {
  id        String   @id @default(cuid())
  rating    Int // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())

  // Relationships
  listingId  String
  reviewerId String
  revieweeId String

  listing  Listing @relation(fields: [listingId], references: [id], onDelete: Cascade)
  reviewer User    @relation(fields: [reviewerId], references: [id], onDelete: Cascade)
  reviewee User    @relation("ReviewGiver", fields: [revieweeId], references: [id], onDelete: Cascade)

  @@unique([listingId, reviewerId])
  @@map("reviews")
}

// User Preferences
model Favorite {
  id        String   @id @default(cuid())
  userId    String
  listingId String
  createdAt DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  listing Listing @relation(fields: [listingId], references: [id], onDelete: Cascade)

  @@unique([userId, listingId])
  @@map("favorites")
}

model SavedSearch {
  id            String   @id @default(cuid())
  userId        String
  query         String
  filters       Json // Store search filters as JSON
  alertsEnabled Boolean  @default(false)
  createdAt     DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("saved_searches")
}

// AI Analytics
model AIAnalytics {
  id         String   @id @default(cuid())
  type       String // 'price_suggestion', 'category_detection', 'image_analysis', etc.
  input      Json // Input data for AI
  output     Json // AI response
  confidence Float? // Confidence score
  model      String // AI model used
  cost       Float? // API cost
  createdAt  DateTime @default(now())

  @@map("ai_analytics")
}

// Enums
enum UserType {
  PRIVATE
  VENDOR
  ADMIN
}

enum Condition {
  NEW
  LIKE_NEW
  GOOD
  FAIR
  POOR
}

enum ListingStatus {
  DRAFT
  ACTIVE
  SOLD
  EXPIRED
  REMOVED
}

enum MessageType {
  USER
  AI_RESPONSE
  SYSTEM
}
