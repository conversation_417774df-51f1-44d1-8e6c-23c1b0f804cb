'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import Link from 'next/link'
import {
  Users,
  Package,
  DollarSign,
  TrendingUp,
  Shield,
  AlertTriangle,
  Activity,
  BarChart3,
  <PERSON>tings,
  Eye
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { QuickStats } from '@/components/dashboard/StatsCard'
import ChartCard, { PlaceholderChart, ChartActions } from '@/components/dashboard/ChartCard'
import ActivityFeed from '@/components/dashboard/ActivityFeed'

interface AdminStats {
  users: {
    total: number
    private: number
    vendors: number
    growth: number
    newLast30Days: number
  }
  listings: {
    total: number
    active: number
    growth: number
    newLast30Days: number
  }
  ai: {
    totalAnalytics: number
    totalCost: number
    averageCost: number
  }
  categories: Array<{
    name: string
    count: number
  }>
  recent: {
    users: Array<{
      id: string
      username: string
      email: string
      userType: string
      createdAt: string
      vendorProfile?: { businessName: string }
    }>
    listings: Array<{
      id: string
      title: string
      price: number
      status: string
      createdAt: string
      user: {
        username: string
        userType: string
      }
    }>
  }
  recentActivity: Array<{
    id: string
    type: 'user' | 'listing' | 'sale' | 'report'
    title: string
    description?: string
    timestamp: string
    metadata?: {
      price?: number
      status?: string
      userType?: string
    }
    href?: string
  }>
}

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login?message=Please sign in to access admin area&redirect=/admin')
      return
    }

    if (status === 'authenticated' && session?.user?.userType !== 'ADMIN') {
      router.push('/?message=Access denied. Admin privileges required.')
      return
    }

    if (status === 'authenticated' && session?.user?.userType === 'ADMIN') {
      fetchStats()
    }
  }, [status, session, router])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats')
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch stats')
      }

      setStats(result.stats)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load admin stats')
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout userType="ADMIN">
        <div className="p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout userType="ADMIN">
        <div className="p-8">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Dashboard</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={fetchStats}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!stats) {
    return null
  }

  const quickStats = [
    {
      title: 'Total Users',
      value: stats.users.total,
      icon: <Users className="w-6 h-6" />,
      color: 'blue' as const,
      change: {
        value: stats.users.growth,
        type: stats.users.growth >= 0 ? 'increase' as const : 'decrease' as const,
        period: 'this month'
      },
      subtitle: `${stats.users.newLast30Days} new this month`
    },
    {
      title: 'Active Listings',
      value: stats.listings.active,
      icon: <Package className="w-6 h-6" />,
      color: 'green' as const,
      change: {
        value: stats.listings.growth,
        type: stats.listings.growth >= 0 ? 'increase' as const : 'decrease' as const,
        period: 'this month'
      },
      subtitle: `${stats.listings.total} total`
    },
    {
      title: 'Vendors',
      value: stats.users.vendors,
      icon: <Shield className="w-6 h-6" />,
      color: 'purple' as const,
      subtitle: `${((stats.users.vendors / stats.users.total) * 100).toFixed(1)}% of users`
    },
    {
      title: 'AI Cost',
      value: `$${stats.ai.totalCost.toFixed(2)}`,
      icon: <BarChart3 className="w-6 h-6" />,
      color: 'orange' as const,
      subtitle: `${stats.ai.totalAnalytics} analyses`
    }
  ]

  return (
    <DashboardLayout userType="ADMIN">
      <div>
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Admin Dashboard 🛡️
              </h1>
              <p className="text-gray-600 mt-2">
                Platform overview and management tools
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                🛡️ Admin Access
              </span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mb-8">
          <QuickStats stats={quickStats} loading={loading} />
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Platform Growth Chart */}
          <ChartCard
            title="Platform Growth"
            subtitle="Users and listings over time"
            actions={<ChartActions />}
          >
            <PlaceholderChart
              type="line"
              title="Growth Trends"
              description="Track platform growth metrics"
            />
          </ChartCard>

          {/* System Health */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-900">Database</span>
                  </div>
                  <span className="text-sm text-green-600">Healthy</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-900">API Services</span>
                  </div>
                  <span className="text-sm text-green-600">Operational</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-900">AI Services</span>
                  </div>
                  <span className="text-sm text-yellow-600">Monitoring</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-900">File Storage</span>
                  </div>
                  <span className="text-sm text-green-600">Healthy</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link
            href="/admin/users"
            className="bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all group"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">Manage Users</h3>
                <p className="text-sm text-gray-600">View and moderate accounts</p>
              </div>
            </div>
          </Link>

          <Link
            href="/admin/listings"
            className="bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all group"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <Package className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">Manage Listings</h3>
                <p className="text-sm text-gray-600">Review and moderate content</p>
              </div>
            </div>
          </Link>

          <Link
            href="/admin/ai"
            className="bg-white border border-gray-200 p-6 rounded-xl shadow-sm hover:shadow-md transition-all group"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">AI Analytics</h3>
                <p className="text-sm text-gray-600">Monitor AI performance</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Recent Users */}
          <ActivityFeed
            title="Recent Users"
            items={stats.recent.users.map(user => ({
              id: user.id,
              type: 'user' as const,
              title: user.vendorProfile?.businessName || user.username,
              description: `${user.userType} • ${user.email}`,
              timestamp: user.createdAt,
              user: {
                name: user.username,
                userType: user.userType
              }
            }))}
            loading={loading}
            emptyMessage="No recent users"
          />

          {/* Recent Listings */}
          <ActivityFeed
            title="Recent Listings"
            items={stats.recent.listings.map(listing => ({
              id: listing.id,
              type: 'listing' as const,
              title: listing.title,
              description: `by ${listing.user.username}`,
              timestamp: listing.createdAt,
              metadata: {
                price: listing.price,
                status: listing.status
              },
              href: `/listings/${listing.id}`
            }))}
            loading={loading}
            emptyMessage="No recent listings"
          />
        </div>

        {/* Category Distribution */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900">Popular Categories</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {stats.categories.slice(0, 10).map((category) => (
                <div key={category.name} className="text-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="text-2xl mb-2">
                    {category.name === 'Electronics' && '📱'}
                    {category.name === 'Furniture' && '🪑'}
                    {category.name === 'Clothing' && '👕'}
                    {category.name === 'Books' && '📚'}
                    {category.name === 'Sports & Outdoors' && '⚽'}
                    {category.name === 'Home & Garden' && '🏡'}
                    {category.name === 'Toys & Games' && '🧸'}
                    {category.name === 'Automotive' && '🚗'}
                    {category.name === 'Health & Beauty' && '💄'}
                    {!['Electronics', 'Furniture', 'Clothing', 'Books', 'Sports & Outdoors', 'Home & Garden', 'Toys & Games', 'Automotive', 'Health & Beauty'].includes(category.name) && '📦'}
                  </div>
                  <p className="text-sm font-medium text-gray-900">{category.name}</p>
                  <p className="text-xs text-gray-500">{category.count} listings</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
