const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    // Hash the password
    const hashedPassword = await bcrypt.hash('password123', 12)

    // Create test user
    const user = await prisma.user.create({
      data: {
        id: 'test-user-123',
        email: '<EMAIL>',
        username: 'testuser',
        password: hashedPassword,
        firstName: 'Test',
        lastName: 'User',
        userType: 'PRIVATE',
        isVerified: true
      }
    })

    console.log('✅ Test user created successfully!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: password123')
    console.log('👤 User ID:', user.id)

    // Also create a vendor test user
    const hashedVendorPassword = await bcrypt.hash('vendor123', 12)
    
    const vendorUser = await prisma.user.create({
      data: {
        id: 'test-vendor-123',
        email: '<EMAIL>',
        username: 'testvendor',
        password: hashedVendorPassword,
        firstName: 'Test',
        lastName: 'Vendor',
        userType: 'VENDOR',
        isVerified: true,
        vendorProfile: {
          create: {
            businessName: 'Test Business',
            description: 'A test business for development',
            verified: true
          }
        }
      },
      include: {
        vendorProfile: true
      }
    })

    console.log('✅ Test vendor created successfully!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: vendor123')
    console.log('🏪 Business:', vendorUser.vendorProfile?.businessName)

  } catch (error) {
    console.error('❌ Error creating test users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
