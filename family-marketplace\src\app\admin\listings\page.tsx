'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  Package, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2, 
  CheckCircle,
  AlertTriangle,
  Clock,
  DollarSign,
  Flag
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'

interface Listing {
  id: string
  title: string
  description: string
  price: number
  category: string
  condition: string
  status: 'DRAFT' | 'ACTIVE' | 'SOLD' | 'EXPIRED' | 'REMOVED'
  createdAt: string
  updatedAt: string
  views: number
  favorites: number
  images: string[]
  user: {
    id: string
    username: string
    userType: string
    vendorProfile?: {
      businessName: string
    }
  }
  flagged: boolean
  flagReason?: string
}

export default function AdminListings() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [listings, setListings] = useState<Listing[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterCategory, setFilterCategory] = useState('all')
  const [selectedListings, setSelectedListings] = useState<string[]>([])

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/login?message=Please sign in to access admin area&redirect=/admin/listings')
      return
    }

    if (session.user.userType !== 'ADMIN') {
      router.push('/?message=Access denied. Admin privileges required.')
      return
    }

    fetchListings()
  }, [session, status, router])

  const fetchListings = async () => {
    try {
      // Mock data for demonstration
      const mockListings: Listing[] = [
        {
          id: '1',
          title: 'iPhone 12 Pro Max 256GB',
          description: 'Excellent condition iPhone 12 Pro Max with original box and accessories.',
          price: 15000,
          category: 'Electronics',
          condition: 'LIKE_NEW',
          status: 'ACTIVE',
          createdAt: '2024-01-20T10:30:00Z',
          updatedAt: '2024-01-20T10:30:00Z',
          views: 245,
          favorites: 12,
          images: ['/api/placeholder/300/200'],
          user: {
            id: '1',
            username: 'john_doe',
            userType: 'PRIVATE'
          },
          flagged: false
        },
        {
          id: '2',
          title: 'MacBook Air M1 2020',
          description: 'Barely used MacBook Air with M1 chip, 8GB RAM, 256GB SSD.',
          price: 18000,
          category: 'Electronics',
          condition: 'GOOD',
          status: 'ACTIVE',
          createdAt: '2024-01-19T14:22:00Z',
          updatedAt: '2024-01-19T14:22:00Z',
          views: 189,
          favorites: 8,
          images: ['/api/placeholder/300/200'],
          user: {
            id: '2',
            username: 'tech_store',
            userType: 'VENDOR',
            vendorProfile: {
              businessName: 'Tech Store SA'
            }
          },
          flagged: true,
          flagReason: 'Suspicious pricing'
        },
        {
          id: '3',
          title: 'Gaming Chair - Ergonomic',
          description: 'Comfortable gaming chair with lumbar support and adjustable height.',
          price: 2500,
          category: 'Furniture',
          condition: 'NEW',
          status: 'SOLD',
          createdAt: '2024-01-18T09:15:00Z',
          updatedAt: '2024-01-20T16:45:00Z',
          views: 156,
          favorites: 5,
          images: ['/api/placeholder/300/200'],
          user: {
            id: '3',
            username: 'furniture_world',
            userType: 'VENDOR',
            vendorProfile: {
              businessName: 'Furniture World'
            }
          },
          flagged: false
        }
      ]
      setListings(mockListings)
    } catch (error) {
      console.error('Failed to fetch listings:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredListings = listings.filter(listing => {
    const matchesSearch = listing.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         listing.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         listing.user.username.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = filterStatus === 'all' || listing.status.toLowerCase() === filterStatus
    const matchesCategory = filterCategory === 'all' || listing.category.toLowerCase() === filterCategory
    
    return matchesSearch && matchesStatus && matchesCategory
  })

  const handleSelectListing = (listingId: string) => {
    setSelectedListings(prev => 
      prev.includes(listingId) 
        ? prev.filter(id => id !== listingId)
        : [...prev, listingId]
    )
  }

  const handleSelectAll = () => {
    setSelectedListings(
      selectedListings.length === filteredListings.length 
        ? [] 
        : filteredListings.map(listing => listing.id)
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'SOLD': return 'bg-blue-100 text-blue-800'
      case 'DRAFT': return 'bg-yellow-100 text-yellow-800'
      case 'EXPIRED': return 'bg-gray-100 text-gray-800'
      case 'REMOVED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <CheckCircle className="w-3 h-3" />
      case 'SOLD': return <DollarSign className="w-3 h-3" />
      case 'DRAFT': return <Clock className="w-3 h-3" />
      case 'EXPIRED': return <AlertTriangle className="w-3 h-3" />
      case 'REMOVED': return <Trash2 className="w-3 h-3" />
      default: return <Package className="w-3 h-3" />
    }
  }

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout userType="ADMIN">
        <div className="p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
            <div className="h-96 bg-gray-200 rounded-xl"></div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!session || session.user.userType !== 'ADMIN') {
    return null
  }

  return (
    <DashboardLayout userType="ADMIN">
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Listing Management 📦</h1>
            <p className="text-gray-600 mt-2">
              Review and moderate marketplace listings
            </p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search listings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="sold">Sold</option>
                <option value="draft">Draft</option>
                <option value="expired">Expired</option>
                <option value="removed">Removed</option>
              </select>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Categories</option>
                <option value="electronics">Electronics</option>
                <option value="furniture">Furniture</option>
                <option value="clothing">Clothing</option>
                <option value="books">Books</option>
                <option value="automotive">Automotive</option>
              </select>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>{filteredListings.length} listings found</span>
              {selectedListings.length > 0 && (
                <span className="text-blue-600">• {selectedListings.length} selected</span>
              )}
            </div>
          </div>
        </div>

        {/* Listings Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredListings.map((listing) => (
            <div key={listing.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
              {/* Image */}
              <div className="relative h-48 bg-gray-200">
                {listing.images[0] ? (
                  <img 
                    src={listing.images[0]} 
                    alt={listing.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    <Package className="w-12 h-12" />
                  </div>
                )}
                
                {/* Flags and Status */}
                <div className="absolute top-3 left-3 flex flex-col space-y-2">
                  {listing.flagged && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      <Flag className="w-3 h-3 mr-1" />
                      Flagged
                    </span>
                  )}
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(listing.status)}`}>
                    {getStatusIcon(listing.status)}
                    <span className="ml-1">{listing.status}</span>
                  </span>
                </div>

                {/* Selection Checkbox */}
                <div className="absolute top-3 right-3">
                  <input
                    type="checkbox"
                    checked={selectedListings.includes(listing.id)}
                    onChange={() => handleSelectListing(listing.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                    {listing.title}
                  </h3>
                  <span className="text-lg font-bold text-green-600 ml-2">
                    R{listing.price.toLocaleString()}
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {listing.description}
                </p>

                {/* Seller Info */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                      {listing.user.vendorProfile?.businessName?.[0] || listing.user.username[0].toUpperCase()}
                    </div>
                    <span className="text-sm text-gray-600">
                      {listing.user.vendorProfile?.businessName || listing.user.username}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      listing.user.userType === 'VENDOR' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                    }`}>
                      {listing.user.userType === 'VENDOR' ? '🏪' : '👤'}
                    </span>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Eye className="w-4 h-4 mr-1" />
                      {listing.views}
                    </span>
                    <span className="flex items-center">
                      ❤️ {listing.favorites}
                    </span>
                  </div>
                  <span>{new Date(listing.createdAt).toLocaleDateString()}</span>
                </div>

                {/* Flag Reason */}
                {listing.flagged && listing.flagReason && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                    <p className="text-sm text-red-800">
                      <strong>Flag Reason:</strong> {listing.flagReason}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <button className="text-gray-400 hover:text-blue-600 p-1">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="text-gray-400 hover:text-green-600 p-1">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="text-gray-400 hover:text-red-600 p-1">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                  <button className="text-gray-400 hover:text-gray-600 p-1">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bulk Actions */}
        {selectedListings.length > 0 && (
          <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">{selectedListings.length} listings selected</span>
              <div className="flex items-center space-x-2">
                <button className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700">
                  Approve
                </button>
                <button className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700">
                  Flag
                </button>
                <button className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700">
                  Remove
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
