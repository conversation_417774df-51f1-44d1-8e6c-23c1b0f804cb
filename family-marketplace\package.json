{"name": "family-marketplace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.10.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.522.0", "multer": "^2.0.1", "next": "15.3.4", "next-auth": "^4.24.11", "openai": "^5.6.0", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "sharp": "^0.34.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}