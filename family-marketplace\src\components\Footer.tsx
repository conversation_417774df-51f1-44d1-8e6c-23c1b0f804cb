import Link from 'next/link'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <h3 className="text-2xl font-bold">🏪 Family Marketplace</h3>
              <span className="ml-2 px-2 py-1 bg-purple-600 text-white text-xs font-medium rounded-full">
                AI-Powered
              </span>
            </div>
            <p className="text-gray-400 mb-6 max-w-md">
              The future of AI-powered marketplace. Smart categorization, intelligent pricing, 
              and automated descriptions make selling effortless for everyone.
            </p>
            
            {/* AI Features Highlight */}
            <div className="space-y-2 mb-6">
              <div className="flex items-center text-sm text-gray-300">
                <span className="mr-2">🤖</span>
                AI-powered listing creation
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <span className="mr-2">💰</span>
                Smart price suggestions
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <span className="mr-2">🔍</span>
                Visual search capabilities
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Facebook</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Twitter</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Instagram</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-400 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/browse" className="text-gray-400 hover:text-white transition-colors">
                  Browse Items
                </Link>
              </li>
              <li>
                <Link href="/create" className="text-gray-400 hover:text-white transition-colors">
                  Sell with AI
                </Link>
              </li>
              <li>
                <Link href="/how-it-works" className="text-gray-400 hover:text-white transition-colors">
                  How It Works
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="text-gray-400 hover:text-white transition-colors">
                  Pricing
                </Link>
              </li>
            </ul>
          </div>

          {/* Account & Support */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Account & Support</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/register?type=private" className="text-gray-400 hover:text-white transition-colors">
                  Join as Private Seller
                </Link>
              </li>
              <li>
                <Link href="/register?type=vendor" className="text-gray-400 hover:text-white transition-colors">
                  Join as Vendor
                </Link>
              </li>
              <li>
                <Link href="/login" className="text-gray-400 hover:text-white transition-colors">
                  Sign In
                </Link>
              </li>
              <li>
                <Link href="/help" className="text-gray-400 hover:text-white transition-colors">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-400 hover:text-white transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/safety" className="text-gray-400 hover:text-white transition-colors">
                  Safety Tips
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Categories Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <h4 className="text-lg font-semibold mb-4">Popular Categories</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[
              { name: 'Electronics', icon: '📱' },
              { name: 'Furniture', icon: '🪑' },
              { name: 'Clothing', icon: '👕' },
              { name: 'Books', icon: '📚' },
              { name: 'Sports', icon: '⚽' },
              { name: 'Home & Garden', icon: '🏡' },
              { name: 'Toys', icon: '🧸' },
              { name: 'Automotive', icon: '🚗' },
              { name: 'Health & Beauty', icon: '💄' },
              { name: 'Collectibles', icon: '🎨' },
              { name: 'Music', icon: '🎵' },
              { name: 'Tools', icon: '🔧' }
            ].map((category) => (
              <Link
                key={category.name}
                href={`/browse?category=${category.name.toLowerCase()}`}
                className="flex items-center text-sm text-gray-400 hover:text-white transition-colors"
              >
                <span className="mr-2">{category.icon}</span>
                {category.name}
              </Link>
            ))}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex flex-wrap items-center space-x-6 mb-4 md:mb-0">
              <Link href="/about" className="text-gray-400 hover:text-white transition-colors text-sm">
                About Us
              </Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors text-sm">
                Terms of Service
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors text-sm">
                Cookie Policy
              </Link>
              <Link href="/accessibility" className="text-gray-400 hover:text-white transition-colors text-sm">
                Accessibility
              </Link>
            </div>
            
            <div className="text-sm text-gray-400">
              © {currentYear} Family Marketplace. All rights reserved.
            </div>
          </div>
        </div>

        {/* AI Powered Badge */}
        <div className="text-center mt-8 pt-4 border-t border-gray-800">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full text-sm font-medium">
            <span className="mr-2">🤖</span>
            Powered by Advanced AI Technology
          </div>
        </div>
      </div>
    </footer>
  )
}
