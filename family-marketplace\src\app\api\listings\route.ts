import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user or create/use mock user
    const session = await getServerSession(authOptions)

    let userId: string

    if (session?.user?.id) {
      userId = session.user.id
    } else {
      // Create or get mock user for testing
      const mockUserId = 'user_mock_123'

      // Check if mock user exists, create if not
      let mockUser = await prisma.user.findUnique({
        where: { id: mockUserId }
      })

      if (!mockUser) {
        mockUser = await prisma.user.create({
          data: {
            id: mockUserId,
            email: '<EMAIL>',
            username: 'mockuser',
            firstName: 'Mock',
            lastName: 'User',
            userType: 'PRIVATE',
            emailVerified: new Date()
          }
        })
      }

      userId = mockUserId
    }

    const body = await request.json()
    const {
      title,
      description,
      price,
      condition,
      category,
      subcategory,
      brand,
      model,
      location,
      images,
      aiGeneratedTitle,
      aiGeneratedDescription,
      aiSuggestedPrice,
      aiConfidenceScore,
      aiTags
    } = body

    // Validation
    if (!title || !description || !price || !condition || !category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    if (!['NEW', 'LIKE_NEW', 'GOOD', 'FAIR', 'POOR'].includes(condition)) {
      return NextResponse.json(
        { error: 'Invalid condition value' },
        { status: 400 }
      )
    }

    if (price < 0) {
      return NextResponse.json(
        { error: 'Price must be positive' },
        { status: 400 }
      )
    }

    // Create the listing
    const listing = await prisma.listing.create({
      data: {
        title,
        description,
        price: parseFloat(price),
        condition,
        category,
        subcategory: subcategory || null,
        brand: brand || null,
        model: model || null,
        location: location || 'South Africa',
        aiGeneratedTitle: aiGeneratedTitle || null,
        aiGeneratedDescription: aiGeneratedDescription || null,
        aiSuggestedPrice: aiSuggestedPrice ? parseFloat(aiSuggestedPrice) : null,
        aiConfidenceScore: aiConfidenceScore ? parseFloat(aiConfidenceScore) : null,
        aiTags: aiTags || null,
        userId: userId,
        images: {
          create: images?.map((image: any, index: number) => ({
            url: image.url,
            altText: `${title} - Image ${index + 1}`,
            isPrimary: index === 0,
            order: index,
            aiAnalysis: image.aiAnalysis || null
          })) || []
        }
      },
      include: {
        images: true,
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            userType: true,
            vendorProfile: {
              select: {
                businessName: true,
                verified: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Listing created successfully',
      listing
    })
  } catch (error) {
    console.error('Listing creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const userType = searchParams.get('userType')
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    const condition = searchParams.get('condition')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      status: 'ACTIVE'
    }

    if (category) {
      where.category = category
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { brand: { contains: search, mode: 'insensitive' } },
        { model: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (userType) {
      where.user = {
        userType: userType
      }
    }

    if (minPrice || maxPrice) {
      where.price = {}
      if (minPrice) where.price.gte = parseFloat(minPrice)
      if (maxPrice) where.price.lte = parseFloat(maxPrice)
    }

    if (condition) {
      where.condition = condition
    }

    // Get listings with pagination
    const [listings, total] = await Promise.all([
      prisma.listing.findMany({
        where,
        include: {
          images: {
            orderBy: { order: 'asc' }
          },
          user: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              userType: true,
              vendorProfile: {
                select: {
                  businessName: true,
                  verified: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.listing.count({ where })
    ])

    return NextResponse.json({
      success: true,
      listings,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasMore: skip + limit < total
      }
    })
  } catch (error) {
    console.error('Listings fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
