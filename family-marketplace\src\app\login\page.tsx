import LoginForm from '@/components/LoginForm'
import Link from 'next/link'

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12">
      {/* Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              🏪 Family Marketplace
            </h1>
            <span className="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
              AI-Powered
            </span>
          </Link>
          
          <Link href="/register" className="text-blue-600 hover:text-blue-700">
            New here? Create account
          </Link>
        </div>
      </div>

      {/* Login Form */}
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
        <LoginForm />
      </div>

      {/* Features Reminder */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">
            Why Choose Family Marketplace?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="text-4xl mb-4">🤖</div>
              <h3 className="font-semibold mb-2">AI-Powered Listings</h3>
              <p className="text-gray-600 text-sm">
                Upload a photo and let AI create perfect listings with smart categorization and pricing
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="font-semibold mb-2">Lightning Fast</h3>
              <p className="text-gray-600 text-sm">
                Create listings in under 2 minutes with our intelligent automation
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="font-semibold mb-2">Smart Matching</h3>
              <p className="text-gray-600 text-sm">
                Advanced search and visual similarity matching connects buyers with perfect items
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
