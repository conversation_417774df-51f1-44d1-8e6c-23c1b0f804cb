'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  Search, 
  ChevronDown, 
  ChevronRight, 
  MessageCircle, 
  Phone, 
  Mail,
  Book,
  Users,
  Zap,
  Shield,
  DollarSign,
  Camera
} from 'lucide-react'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)

  const categories = [
    { id: 'all', name: 'All Topics', icon: Book },
    { id: 'getting-started', name: 'Getting Started', icon: Zap },
    { id: 'selling', name: 'Selling Items', icon: Camera },
    { id: 'buying', name: 'Buying Items', icon: Users },
    { id: 'payments', name: 'Payments & Fees', icon: DollarSign },
    { id: 'safety', name: 'Safety & Security', icon: Shield },
    { id: 'ai-features', name: 'AI Features', icon: Zap }
  ]

  const faqs: FAQItem[] = [
    {
      id: '1',
      category: 'getting-started',
      question: 'How do I create an account?',
      answer: 'Creating an account is simple! Click "Join Now" in the top navigation, choose between Private Seller or Business Vendor, fill in your details, and verify your email address. You can start listing items immediately after registration.'
    },
    {
      id: '2',
      category: 'ai-features',
      question: 'How does the AI listing creation work?',
      answer: 'Our AI analyzes your uploaded photos to automatically identify the product, suggest categories, generate titles and descriptions, and recommend pricing. Simply upload a photo, review the AI suggestions, make any adjustments, and publish your listing in under 2 minutes.'
    },
    {
      id: '3',
      category: 'selling',
      question: 'How do I create a listing?',
      answer: 'Click "Sell with AI" in the navigation (you must be logged in), upload photos of your item, let our AI analyze and suggest details, review and edit the information, set your price, and publish. The AI will handle most of the work for you!'
    },
    {
      id: '4',
      category: 'payments',
      question: 'What fees do you charge?',
      answer: 'We only charge when you make a sale: 2.5% for Private Sellers (min R5, max R500) and 2.0% for Business Vendors (min R5, max R1000). No listing fees, no monthly charges for private sellers. Business plans start at R299/month with reduced transaction fees.'
    },
    {
      id: '5',
      category: 'safety',
      question: 'How do I stay safe when buying or selling?',
      answer: 'Always meet in public places, verify seller/buyer identity, use our secure messaging system, check user ratings and reviews, trust your instincts, and report suspicious activity. Never share personal financial information outside our platform.'
    },
    {
      id: '6',
      category: 'buying',
      question: 'How do I contact a seller?',
      answer: 'Click on any listing to view details, then click "Contact Seller" to send a message. You must be logged in to contact sellers. Use our built-in messaging system to communicate safely and keep records of your conversations.'
    },
    {
      id: '7',
      category: 'ai-features',
      question: 'How accurate is the AI price suggestion?',
      answer: 'Our AI price suggestions are based on current market data and have 92% accuracy. The AI considers factors like item condition, brand, model, category, and recent sales data. You can always adjust the suggested price based on your preferences.'
    },
    {
      id: '8',
      category: 'selling',
      question: 'Can I edit my listing after publishing?',
      answer: 'Yes! Go to "My Listings" in your account menu, find your listing, and click "Edit". You can update photos, descriptions, prices, and other details anytime. Changes are reflected immediately on the marketplace.'
    },
    {
      id: '9',
      category: 'payments',
      question: 'When and how do I get paid?',
      answer: 'Payment arrangements are made directly between buyers and sellers. We recommend secure payment methods like bank transfers or cash on delivery. For Business Vendors, we offer integrated payment processing with automatic fee deduction.'
    },
    {
      id: '10',
      category: 'getting-started',
      question: 'What\'s the difference between Private Seller and Business Vendor?',
      answer: 'Private Sellers are individuals/families selling personal items - it\'s free with basic features. Business Vendors are businesses with advanced features like analytics, bulk management, verified badges, and priority support for R299/month.'
    }
  ]

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Help Center
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Find answers to your questions and get the most out of our AI-powered marketplace
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
                <input
                  type="text"
                  placeholder="Search for help articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg text-lg focus:ring-2 focus:ring-blue-300 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Categories Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Browse by Topic</h3>
              <div className="space-y-2">
                {categories.map((category) => {
                  const Icon = category.icon
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{category.name}</span>
                    </button>
                  )
                })}
              </div>

              {/* Quick Contact */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-4">Need More Help?</h4>
                <div className="space-y-3">
                  <Link
                    href="/contact"
                    className="flex items-center space-x-3 text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    <MessageCircle className="w-5 h-5" />
                    <span>Contact Support</span>
                  </Link>
                  <div className="flex items-center space-x-3 text-gray-600">
                    <Phone className="w-5 h-5" />
                    <span>+27 11 123 4567</span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-600">
                    <Mail className="w-5 h-5" />
                    <span><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900">
                  {selectedCategory === 'all' 
                    ? 'Frequently Asked Questions' 
                    : `${categories.find(c => c.id === selectedCategory)?.name} Questions`
                  }
                </h2>
                <p className="text-gray-600 mt-2">
                  {filteredFAQs.length} {filteredFAQs.length === 1 ? 'question' : 'questions'} found
                </p>
              </div>

              <div className="p-6">
                {filteredFAQs.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 text-6xl mb-4">🔍</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                    <p className="text-gray-600">
                      Try adjusting your search or browse different categories
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredFAQs.map((faq) => (
                      <div key={faq.id} className="border border-gray-200 rounded-lg">
                        <button
                          onClick={() => toggleFAQ(faq.id)}
                          className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                        >
                          <span className="font-medium text-gray-900 pr-4">
                            {faq.question}
                          </span>
                          {expandedFAQ === faq.id ? (
                            <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-500 flex-shrink-0" />
                          )}
                        </button>
                        {expandedFAQ === faq.id && (
                          <div className="px-4 pb-4">
                            <div className="pt-2 border-t border-gray-100">
                              <p className="text-gray-600 leading-relaxed">
                                {faq.answer}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Quick Start Guide */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8 mt-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Quick Start Guide</h3>
              <p className="text-gray-600 mb-6">
                New to Family Marketplace? Follow these simple steps to get started:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 text-white font-bold">
                    1
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Sign Up</h4>
                  <p className="text-gray-600 text-sm">
                    Create your free account as a Private Seller or Business Vendor
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="bg-purple-600 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 text-white font-bold">
                    2
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Upload & Sell</h4>
                  <p className="text-gray-600 text-sm">
                    Take a photo and let our AI create your listing automatically
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="bg-green-600 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 text-white font-bold">
                    3
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Connect & Trade</h4>
                  <p className="text-gray-600 text-sm">
                    Connect with buyers, arrange safe meetups, and complete your sale
                  </p>
                </div>
              </div>
              
              <div className="text-center mt-8">
                <Link
                  href="/how-it-works"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  Learn More About How It Works
                </Link>
              </div>
            </div>

            {/* Popular Articles */}
            <div className="bg-white rounded-lg shadow-sm p-8 mt-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Popular Help Articles</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Link href="#" className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-semibold text-gray-900 mb-2">Setting Up Your Profile</h4>
                    <p className="text-gray-600 text-sm">Complete guide to creating an attractive seller profile</p>
                  </Link>
                  
                  <Link href="#" className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-semibold text-gray-900 mb-2">AI Photography Tips</h4>
                    <p className="text-gray-600 text-sm">How to take photos that work best with our AI</p>
                  </Link>
                  
                  <Link href="#" className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-semibold text-gray-900 mb-2">Pricing Your Items</h4>
                    <p className="text-gray-600 text-sm">Strategies for competitive pricing in South Africa</p>
                  </Link>
                </div>
                
                <div className="space-y-4">
                  <Link href="#" className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-semibold text-gray-900 mb-2">Safe Trading Practices</h4>
                    <p className="text-gray-600 text-sm">Essential safety tips for online marketplace transactions</p>
                  </Link>
                  
                  <Link href="#" className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-semibold text-gray-900 mb-2">Business Vendor Benefits</h4>
                    <p className="text-gray-600 text-sm">Advanced features for growing your business</p>
                  </Link>
                  
                  <Link href="#" className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-semibold text-gray-900 mb-2">Troubleshooting AI Issues</h4>
                    <p className="text-gray-600 text-sm">Common AI problems and how to solve them</p>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
