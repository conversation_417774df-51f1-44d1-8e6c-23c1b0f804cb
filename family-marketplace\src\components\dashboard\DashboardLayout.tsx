'use client'

import { ReactNode, useState } from 'react'
import { useSession } from 'next-auth/react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import Breadcrumbs, { generateBreadcrumbs } from './Breadcrumbs'
import { 
  Home, 
  Package, 
  BarChart3, 
  Users, 
  Settings, 
  Heart, 
  MessageCircle, 
  Plus,
  Menu,
  X,
  Shield,
  Store,
  User,
  Bell,
  Search,
  LogOut
} from 'lucide-react'
import { signOut } from 'next-auth/react'

interface DashboardLayoutProps {
  children: ReactNode
  userType: 'PRIVATE' | 'VENDOR' | 'ADMIN'
}

interface NavItem {
  name: string
  href: string
  icon: any
  badge?: number
}

export default function DashboardLayout({ children, userType }: DashboardLayoutProps) {
  const { data: session } = useSession()
  const pathname = usePathname()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const getNavigationItems = (): NavItem[] => {
    const baseItems: NavItem[] = [
      { name: 'Overview', href: getDashboardPath(), icon: Home },
      { name: 'My Listings', href: '/listings', icon: Package },
      { name: 'Messages', href: '/messages', icon: MessageCircle, badge: 3 },
      { name: 'Favorites', href: '/favorites', icon: Heart },
    ]

    if (userType === 'PRIVATE') {
      return [
        ...baseItems,
        { name: 'Create Listing', href: '/create', icon: Plus },
        { name: 'Profile', href: '/profile', icon: User },
        { name: 'Settings', href: '/settings', icon: Settings },
      ]
    }

    if (userType === 'VENDOR') {
      return [
        ...baseItems,
        { name: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 },
        { name: 'Create Listing', href: '/create', icon: Plus },
        { name: 'Store Profile', href: '/dashboard/store', icon: Store },
        { name: 'Settings', href: '/settings', icon: Settings },
      ]
    }

    if (userType === 'ADMIN') {
      return [
        { name: 'Overview', href: '/admin', icon: Home },
        { name: 'Users', href: '/admin/users', icon: Users },
        { name: 'Listings', href: '/admin/listings', icon: Package },
        { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },
        { name: 'AI Monitoring', href: '/admin/ai', icon: Shield },
        { name: 'Settings', href: '/admin/settings', icon: Settings },
      ]
    }

    return baseItems
  }

  const getDashboardPath = () => {
    switch (userType) {
      case 'ADMIN': return '/admin'
      case 'VENDOR': return '/dashboard'
      case 'PRIVATE': return '/dashboard/private'
      default: return '/dashboard'
    }
  }

  const getDashboardTitle = () => {
    switch (userType) {
      case 'ADMIN': return 'Admin Dashboard'
      case 'VENDOR': return 'Vendor Dashboard'
      case 'PRIVATE': return 'My Dashboard'
      default: return 'Dashboard'
    }
  }

  const getUserTypeColor = () => {
    switch (userType) {
      case 'ADMIN': return 'bg-red-100 text-red-800'
      case 'VENDOR': return 'bg-purple-100 text-purple-800'
      case 'PRIVATE': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getUserTypeIcon = () => {
    switch (userType) {
      case 'ADMIN': return '🛡️'
      case 'VENDOR': return '🏪'
      case 'PRIVATE': return '👤'
      default: return '👤'
    }
  }

  const navigationItems = getNavigationItems()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">{getDashboardTitle()}</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-4 py-4">
            <ul className="space-y-2">
              {navigationItems.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      pathname === item.href
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.name}
                    {item.badge && (
                      <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm">
          <div className="flex items-center h-16 px-4 border-b border-gray-200">
            <Link href="/" className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                FM
              </div>
              <span className="ml-2 text-lg font-semibold text-gray-900">Family Market</span>
            </Link>
          </div>
          
          <div className="flex-1 flex flex-col">
            <nav className="flex-1 px-4 py-4">
              <ul className="space-y-2">
                {navigationItems.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                        pathname === item.href
                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <item.icon className="mr-3 h-5 w-5" />
                      {item.name}
                      {item.badge && (
                        <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>

            {/* User info */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                  {session?.user?.firstName?.[0] || session?.user?.username?.[0] || 'U'}
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {session?.user?.firstName || session?.user?.username}
                  </p>
                  <div className="flex items-center mt-1">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUserTypeColor()}`}>
                      {getUserTypeIcon()} {userType}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => signOut()}
                  className="text-gray-400 hover:text-gray-600"
                  title="Sign out"
                >
                  <LogOut className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-gray-500 hover:text-gray-600 lg:hidden"
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <button className="relative text-gray-400 hover:text-gray-600">
                <Bell className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  3
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="p-8">
            <Breadcrumbs
              items={generateBreadcrumbs(pathname, userType)}
              userType={userType}
            />
            <div className="-mt-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
