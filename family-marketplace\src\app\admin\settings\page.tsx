'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Shield, 
  DollarSign, 
  Mail, 
  Globe,
  Database,
  Key,
  Bell,
  Users,
  Package
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'

interface PlatformSettings {
  general: {
    siteName: string
    siteDescription: string
    contactEmail: string
    supportEmail: string
    maintenanceMode: boolean
  }
  marketplace: {
    commissionRate: number
    maxListingImages: number
    listingDuration: number
    autoApproveListings: boolean
    requireVerification: boolean
  }
  ai: {
    openaiApiKey: string
    maxTokensPerRequest: number
    enableImageAnalysis: boolean
    enablePriceSuggestions: boolean
    costLimit: number
  }
  notifications: {
    emailNotifications: boolean
    smsNotifications: boolean
    pushNotifications: boolean
    adminAlerts: boolean
  }
  security: {
    requireEmailVerification: boolean
    enableTwoFactor: boolean
    sessionTimeout: number
    maxLoginAttempts: number
  }
}

export default function AdminSettings() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [settings, setSettings] = useState<PlatformSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('general')

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/login?message=Please sign in to access admin area&redirect=/admin/settings')
      return
    }

    if (session.user.userType !== 'ADMIN') {
      router.push('/?message=Access denied. Admin privileges required.')
      return
    }

    fetchSettings()
  }, [session, status, router])

  const fetchSettings = async () => {
    try {
      // Mock data for demonstration
      const mockSettings: PlatformSettings = {
        general: {
          siteName: 'Family Marketplace',
          siteDescription: 'South Africa\'s premier family-friendly marketplace',
          contactEmail: '<EMAIL>',
          supportEmail: '<EMAIL>',
          maintenanceMode: false
        },
        marketplace: {
          commissionRate: 5.0,
          maxListingImages: 10,
          listingDuration: 30,
          autoApproveListings: false,
          requireVerification: true
        },
        ai: {
          openaiApiKey: 'sk-proj-lWvQ74OwRr_AUnV4y59jnT...',
          maxTokensPerRequest: 4000,
          enableImageAnalysis: true,
          enablePriceSuggestions: true,
          costLimit: 1000
        },
        notifications: {
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: true,
          adminAlerts: true
        },
        security: {
          requireEmailVerification: true,
          enableTwoFactor: false,
          sessionTimeout: 24,
          maxLoginAttempts: 5
        }
      }
      setSettings(mockSettings)
    } catch (error) {
      console.error('Failed to fetch settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Here you would make an API call to save the settings
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      // Show success message
    } catch (error) {
      console.error('Failed to save settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const updateSetting = (section: keyof PlatformSettings, key: string, value: any) => {
    if (!settings) return
    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [key]: value
      }
    })
  }

  const tabs = [
    { id: 'general', name: 'General', icon: Globe },
    { id: 'marketplace', name: 'Marketplace', icon: Package },
    { id: 'ai', name: 'AI Settings', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Key }
  ]

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout userType="ADMIN">
        <div className="p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
            <div className="h-96 bg-gray-200 rounded-xl"></div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!session || session.user.userType !== 'ADMIN' || !settings) {
    return null
  }

  return (
    <DashboardLayout userType="ADMIN">
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Platform Settings ⚙️</h1>
            <p className="text-gray-600 mt-2">
              Configure platform-wide settings and preferences
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={fetchSettings}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Save className={`w-4 h-4 ${saving ? 'animate-spin' : ''}`} />
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:w-64">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-5 h-5" />
                  <span className="font-medium">{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Settings Content */}
          <div className="flex-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              {/* General Settings */}
              {activeTab === 'general' && (
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">General Settings</h3>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Site Name
                      </label>
                      <input
                        type="text"
                        value={settings.general.siteName}
                        onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Site Description
                      </label>
                      <textarea
                        value={settings.general.siteDescription}
                        onChange={(e) => updateSetting('general', 'siteDescription', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Contact Email
                        </label>
                        <input
                          type="email"
                          value={settings.general.contactEmail}
                          onChange={(e) => updateSetting('general', 'contactEmail', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Support Email
                        </label>
                        <input
                          type="email"
                          value={settings.general.supportEmail}
                          onChange={(e) => updateSetting('general', 'supportEmail', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="maintenanceMode"
                        checked={settings.general.maintenanceMode}
                        onChange={(e) => updateSetting('general', 'maintenanceMode', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="maintenanceMode" className="ml-2 text-sm text-gray-700">
                        Enable Maintenance Mode
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {/* Marketplace Settings */}
              {activeTab === 'marketplace' && (
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Marketplace Settings</h3>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Commission Rate (%)
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={settings.marketplace.commissionRate}
                          onChange={(e) => updateSetting('marketplace', 'commissionRate', parseFloat(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Max Listing Images
                        </label>
                        <input
                          type="number"
                          value={settings.marketplace.maxListingImages}
                          onChange={(e) => updateSetting('marketplace', 'maxListingImages', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Listing Duration (days)
                      </label>
                      <input
                        type="number"
                        value={settings.marketplace.listingDuration}
                        onChange={(e) => updateSetting('marketplace', 'listingDuration', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="autoApprove"
                          checked={settings.marketplace.autoApproveListings}
                          onChange={(e) => updateSetting('marketplace', 'autoApproveListings', e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor="autoApprove" className="ml-2 text-sm text-gray-700">
                          Auto-approve new listings
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="requireVerification"
                          checked={settings.marketplace.requireVerification}
                          onChange={(e) => updateSetting('marketplace', 'requireVerification', e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor="requireVerification" className="ml-2 text-sm text-gray-700">
                          Require vendor verification
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* AI Settings */}
              {activeTab === 'ai' && (
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">AI Settings</h3>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        OpenAI API Key
                      </label>
                      <input
                        type="password"
                        value={settings.ai.openaiApiKey}
                        onChange={(e) => updateSetting('ai', 'openaiApiKey', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Max Tokens per Request
                        </label>
                        <input
                          type="number"
                          value={settings.ai.maxTokensPerRequest}
                          onChange={(e) => updateSetting('ai', 'maxTokensPerRequest', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Monthly Cost Limit ($)
                        </label>
                        <input
                          type="number"
                          value={settings.ai.costLimit}
                          onChange={(e) => updateSetting('ai', 'costLimit', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="enableImageAnalysis"
                          checked={settings.ai.enableImageAnalysis}
                          onChange={(e) => updateSetting('ai', 'enableImageAnalysis', e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor="enableImageAnalysis" className="ml-2 text-sm text-gray-700">
                          Enable AI image analysis
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="enablePriceSuggestions"
                          checked={settings.ai.enablePriceSuggestions}
                          onChange={(e) => updateSetting('ai', 'enablePriceSuggestions', e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label htmlFor="enablePriceSuggestions" className="ml-2 text-sm text-gray-700">
                          Enable AI price suggestions
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Add other tab contents similarly */}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
