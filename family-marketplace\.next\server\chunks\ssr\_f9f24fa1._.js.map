{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"bg-gradient-to-br from-blue-50 to-purple-50\">\n      {/* Hero Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            The Future of\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600\">\n              {\" \"}AI-Powered{\" \"}\n            </span>\n            Marketplace\n          </h1>\n\n          <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            Snap a photo, and let AI create your perfect listing. Smart categorization,\n            intelligent pricing, and automated descriptions make selling effortless.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n            <Link\n              href=\"/create\"\n              className=\"bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-105\"\n            >\n              🤖 Create AI Listing\n            </Link>\n            <Link\n              href=\"/browse\"\n              className=\"border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-lg font-semibold text-lg hover:border-gray-400 hover:bg-gray-50 transition-all\"\n            >\n              Browse Items\n            </Link>\n          </div>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n          <div className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\n            <div className=\"text-4xl mb-4\">📸</div>\n            <h3 className=\"text-xl font-semibold mb-2\">Smart Photo Analysis</h3>\n            <p className=\"text-gray-600\">\n              Upload a photo and AI instantly identifies category, brand, condition, and suggests the perfect title and description.\n            </p>\n          </div>\n\n          <div className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\n            <div className=\"text-4xl mb-4\">💰</div>\n            <h3 className=\"text-xl font-semibold mb-2\">Intelligent Pricing</h3>\n            <p className=\"text-gray-600\">\n              AI analyzes market data to suggest competitive prices based on item condition, brand, and current demand.\n            </p>\n          </div>\n\n          <div className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\n            <div className=\"text-4xl mb-4\">🔍</div>\n            <h3 className=\"text-xl font-semibold mb-2\">Visual Search</h3>\n            <p className=\"text-gray-600\">\n              Find similar items by uploading a photo. Perfect for price comparison and discovering alternatives.\n            </p>\n          </div>\n        </div>\n\n        {/* Demo Section */}\n        <div className=\"bg-white rounded-2xl p-8 shadow-xl\">\n          <div className=\"text-center mb-8\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              See AI in Action\n            </h2>\n            <p className=\"text-gray-600\">\n              Experience the magic of AI-powered listing creation\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n            <div className=\"space-y-6\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-purple-100 rounded-full p-2 flex-shrink-0\">\n                  <span className=\"text-purple-600 font-bold\">1</span>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold\">Upload Your Photo</h4>\n                  <p className=\"text-gray-600 text-sm\">Simply drag and drop or click to upload product images</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-purple-100 rounded-full p-2 flex-shrink-0\">\n                  <span className=\"text-purple-600 font-bold\">2</span>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold\">AI Analyzes Everything</h4>\n                  <p className=\"text-gray-600 text-sm\">Our AI identifies category, brand, condition, and creates descriptions</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-purple-100 rounded-full p-2 flex-shrink-0\">\n                  <span className=\"text-purple-600 font-bold\">3</span>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold\">Smart Price Suggestions</h4>\n                  <p className=\"text-gray-600 text-sm\">Get competitive pricing based on market analysis</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"bg-purple-100 rounded-full p-2 flex-shrink-0\">\n                  <span className=\"text-purple-600 font-bold\">4</span>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold\">Publish & Sell</h4>\n                  <p className=\"text-gray-600 text-sm\">Review, adjust, and publish your optimized listing</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl p-6 text-center\">\n              <div className=\"text-6xl mb-4\">🤖</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Ready to try it?</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Create your first AI-powered listing in under 2 minutes\n              </p>\n              <Link\n                href=\"/create\"\n                className=\"inline-block bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all\"\n              >\n                Start Creating →\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mt-16\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-purple-600\">95%</div>\n            <div className=\"text-gray-600\">Accuracy Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">2min</div>\n            <div className=\"text-gray-600\">Average Listing Time</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-green-600\">30%</div>\n            <div className=\"text-gray-600\">Faster Sales</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-orange-600\">24/7</div>\n            <div className=\"text-gray-600\">AI Assistant</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CAEhE,8OAAC;oCAAK,WAAU;;wCACb;wCAAI;wCAAW;;;;;;;gCACX;;;;;;;sCAIT,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAK5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAOjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;8DAE9C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;8DAE9C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;8DAE9C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;8DAE9C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAClD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}