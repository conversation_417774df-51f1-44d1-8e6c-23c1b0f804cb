'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import ImageUpload from './ImageUpload'
import { UploadResult } from '@/types'

// Local interfaces to avoid import issues
interface AIAnalysisResult {
  category: string
  subcategory?: string
  title: string
  description: string
  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'
  brand?: string
  model?: string
  tags: string[]
  confidence: number
}

interface PriceSuggestion {
  suggestedPrice: number
  priceRange: { min: number; max: number }
  confidence: number
  reasoning: string
}

interface FormData {
  title: string
  description: string
  price: number
  condition: string
  category: string
  subcategory: string
  brand: string
  model: string
  location: string
  images: UploadResult[]
}

const CATEGORIES = [
  'Electronics',
  'Furniture',
  'Clothing',
  'Books',
  'Sports & Outdoors',
  'Home & Garden',
  'Toys & Games',
  'Automotive',
  'Health & Beauty',
  'Other'
]

const CONDITIONS = [
  { value: 'NEW', label: 'New' },
  { value: 'LIKE_NEW', label: 'Like New' },
  { value: 'GOOD', label: 'Good' },
  { value: 'FAIR', label: 'Fair' },
  { value: 'POOR', label: 'Poor' }
]

export default function CreateListingForm() {
  const { data: session } = useSession()

  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    price: 0,
    condition: 'GOOD',
    category: '',
    subcategory: '',
    brand: '',
    model: '',
    location: '',
    images: []
  })

  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysisResult | null>(null)
  const [priceSuggestion, setPriceSuggestion] = useState<PriceSuggestion | null>(null)
  const [isLoadingPrice, setIsLoadingPrice] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showAiSuggestions, setShowAiSuggestions] = useState(true)

  // Handle AI analysis results
  const handleAiAnalysis = (analysis: AIAnalysisResult) => {
    setAiAnalysis(analysis)
    
    // Auto-fill form with AI suggestions if user wants
    if (showAiSuggestions) {
      setFormData(prev => ({
        ...prev,
        title: analysis.title || prev.title,
        description: analysis.description || prev.description,
        category: analysis.category || prev.category,
        subcategory: analysis.subcategory || prev.subcategory,
        brand: analysis.brand || prev.brand,
        model: analysis.model || prev.model,
        condition: analysis.condition || prev.condition
      }))
    }
  }

  // Get price suggestion when enough data is available
  useEffect(() => {
    const getPriceSuggestion = async () => {
      if (!formData.category || !formData.condition || !formData.description) {
        return
      }

      setIsLoadingPrice(true)
      try {
        const response = await fetch('/api/ai/price', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            category: formData.category,
            subcategory: formData.subcategory,
            brand: formData.brand,
            model: formData.model,
            condition: formData.condition,
            description: formData.description
          })
        })

        const result = await response.json()
        if (result.success) {
          setPriceSuggestion(result.data)
          
          // Auto-fill price if user hasn't set one
          if (formData.price === 0 && showAiSuggestions) {
            setFormData(prev => ({
              ...prev,
              price: result.data.suggestedPrice
            }))
          }
        }
      } catch (error) {
        console.error('Price suggestion failed:', error)
      } finally {
        setIsLoadingPrice(false)
      }
    }

    // Debounce the price suggestion
    const timer = setTimeout(getPriceSuggestion, 1000)
    return () => clearTimeout(timer)
  }, [formData.category, formData.condition, formData.description, formData.brand, formData.model, formData.subcategory])

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleImageUpload = (result: UploadResult) => {
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, result]
    }))
  }

  const applySuggestion = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const listingData = {
        title: formData.title,
        description: formData.description,
        price: formData.price,
        condition: formData.condition,
        category: formData.category,
        subcategory: formData.subcategory,
        brand: formData.brand,
        model: formData.model,
        location: formData.location,
        images: formData.images,
        // Include AI data if available
        aiGeneratedTitle: aiAnalysis?.title,
        aiGeneratedDescription: aiAnalysis?.description,
        aiSuggestedPrice: priceSuggestion?.suggestedPrice,
        aiConfidenceScore: aiAnalysis?.confidence,
        aiTags: aiAnalysis?.tags
      }

      const response = await fetch('/api/listings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(listingData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create listing')
      }

      alert('Listing created successfully! 🎉')

      // Reset form
      setFormData({
        title: '',
        description: '',
        price: 0,
        condition: 'GOOD',
        category: '',
        subcategory: '',
        brand: '',
        model: '',
        location: '',
        images: []
      })
      setAiAnalysis(null)
      setPriceSuggestion(null)
    } catch (error) {
      console.error('Submission failed:', error)
      alert(error instanceof Error ? error.message : 'Failed to create listing. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Create New Listing
        </h1>
        <p className="text-gray-600">
          🤖 Upload photos and let AI help you create the perfect listing
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Image Upload Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Product Images</h2>
          <ImageUpload
            onUpload={handleImageUpload}
            onAnalysis={handleAiAnalysis}
            maxFiles={5}
          />
        </div>

        {/* AI Analysis Results */}
        {aiAnalysis && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <div className="text-2xl mr-2">🤖</div>
              <h3 className="text-lg font-semibold text-purple-800">
                AI Analysis Results
              </h3>
              <span className="ml-auto text-sm text-purple-600">
                Confidence: {Math.round(aiAnalysis.confidence * 100)}%
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Suggested Title:</strong>
                <p className="text-gray-700">{aiAnalysis.title}</p>
                {aiAnalysis.title !== formData.title && (
                  <button
                    type="button"
                    onClick={() => applySuggestion('title', aiAnalysis.title)}
                    className="text-blue-600 hover:underline text-xs"
                  >
                    Apply suggestion
                  </button>
                )}
              </div>
              
              <div>
                <strong>Category:</strong>
                <p className="text-gray-700">{aiAnalysis.category}</p>
                {aiAnalysis.category !== formData.category && (
                  <button
                    type="button"
                    onClick={() => applySuggestion('category', aiAnalysis.category)}
                    className="text-blue-600 hover:underline text-xs"
                  >
                    Apply suggestion
                  </button>
                )}
              </div>
              
              {aiAnalysis.brand && (
                <div>
                  <strong>Brand:</strong>
                  <p className="text-gray-700">{aiAnalysis.brand}</p>
                  {aiAnalysis.brand !== formData.brand && (
                    <button
                      type="button"
                      onClick={() => applySuggestion('brand', aiAnalysis.brand)}
                      className="text-blue-600 hover:underline text-xs"
                    >
                      Apply suggestion
                    </button>
                  )}
                </div>
              )}
              
              <div>
                <strong>Condition:</strong>
                <p className="text-gray-700">{aiAnalysis.condition}</p>
                {aiAnalysis.condition !== formData.condition && (
                  <button
                    type="button"
                    onClick={() => applySuggestion('condition', aiAnalysis.condition)}
                    className="text-blue-600 hover:underline text-xs"
                  >
                    Apply suggestion
                  </button>
                )}
              </div>
            </div>
            
            <div className="mt-4">
              <strong>Suggested Description:</strong>
              <p className="text-gray-700 text-sm mt-1">{aiAnalysis.description}</p>
              {aiAnalysis.description !== formData.description && (
                <button
                  type="button"
                  onClick={() => applySuggestion('description', aiAnalysis.description)}
                  className="text-blue-600 hover:underline text-xs mt-1"
                >
                  Apply suggestion
                </button>
              )}
            </div>
          </div>
        )}

        {/* Form Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Title */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter a descriptive title for your item"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              required
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a category</option>
              {CATEGORIES.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Condition */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Condition *
            </label>
            <select
              required
              value={formData.condition}
              onChange={(e) => handleInputChange('condition', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {CONDITIONS.map(condition => (
                <option key={condition.value} value={condition.value}>
                  {condition.label}
                </option>
              ))}
            </select>
          </div>

          {/* Brand */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Brand
            </label>
            <input
              type="text"
              value={formData.brand}
              onChange={(e) => handleInputChange('brand', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Brand name"
            />
          </div>

          {/* Model */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <input
              type="text"
              value={formData.model}
              onChange={(e) => handleInputChange('model', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Model name/number"
            />
          </div>
        </div>

        {/* Price Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-700">
              Price * ($)
            </label>
            {isLoadingPrice && (
              <span className="text-sm text-blue-600">🤖 Getting price suggestion...</span>
            )}
          </div>
          
          <input
            type="number"
            required
            min="0"
            step="0.01"
            value={formData.price}
            onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="0.00"
          />

          {/* Price Suggestion */}
          {priceSuggestion && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <span className="text-green-600 font-medium">💡 AI Price Suggestion</span>
                <span className="ml-auto text-sm text-green-600">
                  Confidence: {Math.round(priceSuggestion.confidence * 100)}%
                </span>
              </div>
              <div className="text-sm text-green-700">
                <p><strong>Suggested Price:</strong> ${priceSuggestion.suggestedPrice.toFixed(2)}</p>
                <p><strong>Price Range:</strong> ${priceSuggestion.priceRange.min.toFixed(2)} - ${priceSuggestion.priceRange.max.toFixed(2)}</p>
                <p className="mt-2"><strong>Reasoning:</strong> {priceSuggestion.reasoning}</p>
                {priceSuggestion.suggestedPrice !== formData.price && (
                  <button
                    type="button"
                    onClick={() => applySuggestion('price', priceSuggestion.suggestedPrice)}
                    className="text-green-600 hover:underline text-sm mt-2"
                  >
                    Apply suggested price
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            required
            rows={6}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe your item in detail..."
          />
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Location *
          </label>
          <input
            type="text"
            required
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="City, State"
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Save as Draft
          </button>
          <button
            type="submit"
            disabled={isSubmitting || formData.images.length === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Creating Listing...' : 'Create Listing'}
          </button>
        </div>
      </form>
    </div>
  )
}
