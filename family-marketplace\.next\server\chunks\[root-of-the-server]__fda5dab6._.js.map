{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/listings/%5Bid%5D/view/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    // Increment view count for the listing\n    await prisma.listing.update({\n      where: {\n        id: params.id,\n        status: 'ACTIVE'\n      },\n      data: {\n        views: {\n          increment: 1\n        }\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: 'View count updated'\n    })\n  } catch (error) {\n    console.error('View count update error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,uCAAuC;QACvC,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ;YACV;YACA,MAAM;gBACJ,OAAO;oBACL,WAAW;gBACb;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}