{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          },\n          include: {\n            vendorProfile: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          userType: user.userType,\n          isVerified: user.isVerified,\n          avatar: user.avatar,\n          vendorProfile: user.vendorProfile\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n        token.username = user.username\n        token.userType = user.userType\n        token.isVerified = user.isVerified\n        token.vendorProfile = user.vendorProfile\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n        session.user.username = token.username as string\n        session.user.userType = token.userType as string\n        session.user.isVerified = token.isVerified as boolean\n        session.user.vendorProfile = token.vendorProfile as any\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/login',\n    signUp: '/register'\n  }\n}\n\n// Helper functions for user management\nexport async function createUser(userData: {\n  email: string\n  username: string\n  password: string\n  firstName?: string\n  lastName?: string\n  userType: 'PRIVATE' | 'VENDOR'\n  vendorData?: {\n    businessName: string\n    description?: string\n    website?: string\n  }\n}) {\n  const hashedPassword = await bcrypt.hash(userData.password, 12)\n\n  const user = await prisma.user.create({\n    data: {\n      email: userData.email,\n      username: userData.username,\n      password: hashedPassword,\n      firstName: userData.firstName,\n      lastName: userData.lastName,\n      userType: userData.userType,\n      vendorProfile: userData.userType === 'VENDOR' && userData.vendorData ? {\n        create: {\n          businessName: userData.vendorData.businessName,\n          description: userData.vendorData.description,\n          website: userData.vendorData.website\n        }\n      } : undefined\n    },\n    include: {\n      vendorProfile: true\n    }\n  })\n\n  return user\n}\n\nexport async function getUserByEmail(email: string) {\n  return await prisma.user.findUnique({\n    where: { email },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n\nexport async function getUserByUsername(username: string) {\n  return await prisma.user.findUnique({\n    where: { username },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;oBACA,SAAS;wBACP,eAAe;oBACjB;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,aAAa;gBACnC;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,aAAa,GAAG,KAAK,aAAa;YAC1C;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;YAClD;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,eAAe,WAAW,QAYhC;IACC,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;IAE5D,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM;YACJ,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;YAC3B,UAAU;YACV,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;YAC3B,eAAe,SAAS,QAAQ,KAAK,YAAY,SAAS,UAAU,GAAG;gBACrE,QAAQ;oBACN,cAAc,SAAS,UAAU,CAAC,YAAY;oBAC9C,aAAa,SAAS,UAAU,CAAC,WAAW;oBAC5C,SAAS,SAAS,UAAU,CAAC,OAAO;gBACtC;YACF,IAAI;QACN;QACA,SAAS;YACP,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,KAAa;IAChD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAM;QACf,SAAS;YACP,eAAe;QACjB;IACF;AACF;AAEO,eAAe,kBAAkB,QAAgB;IACtD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,eAAe;QACjB;IACF;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/listings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Get authenticated user or create/use mock user\n    const session = await getServerSession(authOptions)\n\n    let userId: string\n\n    if (session?.user?.id) {\n      userId = session.user.id\n    } else {\n      // Create or get mock user for testing\n      const mockUserId = 'user_mock_123'\n\n      // Check if mock user exists, create if not\n      let mockUser = await prisma.user.findUnique({\n        where: { id: mockUserId }\n      })\n\n      if (!mockUser) {\n        mockUser = await prisma.user.create({\n          data: {\n            id: mockUserId,\n            email: '<EMAIL>',\n            username: 'mockuser',\n            firstName: 'Mock',\n            lastName: 'User',\n            userType: 'PRIVATE',\n            emailVerified: new Date()\n          }\n        })\n      }\n\n      userId = mockUserId\n    }\n\n    const body = await request.json()\n    const {\n      title,\n      description,\n      price,\n      condition,\n      category,\n      subcategory,\n      brand,\n      model,\n      location,\n      images,\n      aiGeneratedTitle,\n      aiGeneratedDescription,\n      aiSuggestedPrice,\n      aiConfidenceScore,\n      aiTags\n    } = body\n\n    // Validation\n    if (!title || !description || !price || !condition || !category) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      )\n    }\n\n    if (!['NEW', 'LIKE_NEW', 'GOOD', 'FAIR', 'POOR'].includes(condition)) {\n      return NextResponse.json(\n        { error: 'Invalid condition value' },\n        { status: 400 }\n      )\n    }\n\n    if (price < 0) {\n      return NextResponse.json(\n        { error: 'Price must be positive' },\n        { status: 400 }\n      )\n    }\n\n    // Create the listing\n    const listing = await prisma.listing.create({\n      data: {\n        title,\n        description,\n        price: parseFloat(price),\n        condition,\n        category,\n        subcategory: subcategory || null,\n        brand: brand || null,\n        model: model || null,\n        location: location || 'South Africa',\n        aiGeneratedTitle: aiGeneratedTitle || null,\n        aiGeneratedDescription: aiGeneratedDescription || null,\n        aiSuggestedPrice: aiSuggestedPrice ? parseFloat(aiSuggestedPrice) : null,\n        aiConfidenceScore: aiConfidenceScore ? parseFloat(aiConfidenceScore) : null,\n        aiTags: aiTags || null,\n        userId: userId,\n        images: {\n          create: images?.map((image: any, index: number) => ({\n            url: image.url,\n            altText: `${title} - Image ${index + 1}`,\n            isPrimary: index === 0,\n            order: index,\n            aiAnalysis: image.aiAnalysis || null\n          })) || []\n        }\n      },\n      include: {\n        images: true,\n        user: {\n          select: {\n            id: true,\n            username: true,\n            firstName: true,\n            lastName: true,\n            userType: true,\n            vendorProfile: {\n              select: {\n                businessName: true,\n                verified: true\n              }\n            }\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: 'Listing created successfully',\n      listing\n    })\n  } catch (error) {\n    console.error('Listing creation error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '12')\n    const category = searchParams.get('category')\n    const search = searchParams.get('search')\n    const userType = searchParams.get('userType')\n    const minPrice = searchParams.get('minPrice')\n    const maxPrice = searchParams.get('maxPrice')\n    const condition = searchParams.get('condition')\n\n    const skip = (page - 1) * limit\n\n    // Build where clause\n    const where: any = {\n      status: 'ACTIVE'\n    }\n\n    if (category) {\n      where.category = category\n    }\n\n    if (search) {\n      where.OR = [\n        { title: { contains: search, mode: 'insensitive' } },\n        { description: { contains: search, mode: 'insensitive' } },\n        { brand: { contains: search, mode: 'insensitive' } },\n        { model: { contains: search, mode: 'insensitive' } }\n      ]\n    }\n\n    if (userType) {\n      where.user = {\n        userType: userType\n      }\n    }\n\n    if (minPrice || maxPrice) {\n      where.price = {}\n      if (minPrice) where.price.gte = parseFloat(minPrice)\n      if (maxPrice) where.price.lte = parseFloat(maxPrice)\n    }\n\n    if (condition) {\n      where.condition = condition\n    }\n\n    // Get listings with pagination\n    const [listings, total] = await Promise.all([\n      prisma.listing.findMany({\n        where,\n        include: {\n          images: {\n            orderBy: { order: 'asc' }\n          },\n          user: {\n            select: {\n              id: true,\n              username: true,\n              firstName: true,\n              lastName: true,\n              userType: true,\n              vendorProfile: {\n                select: {\n                  businessName: true,\n                  verified: true\n                }\n              }\n            }\n          }\n        },\n        orderBy: {\n          createdAt: 'desc'\n        },\n        skip,\n        take: limit\n      }),\n      prisma.listing.count({ where })\n    ])\n\n    return NextResponse.json({\n      success: true,\n      listings,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n        hasMore: skip + limit < total\n      }\n    })\n  } catch (error) {\n    console.error('Listings fetch error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,iDAAiD;QACjD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI;QAEJ,IAAI,SAAS,MAAM,IAAI;YACrB,SAAS,QAAQ,IAAI,CAAC,EAAE;QAC1B,OAAO;YACL,sCAAsC;YACtC,MAAM,aAAa;YAEnB,2CAA2C;YAC3C,IAAI,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC1C,OAAO;oBAAE,IAAI;gBAAW;YAC1B;YAEA,IAAI,CAAC,UAAU;gBACb,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBAClC,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,eAAe,IAAI;oBACrB;gBACF;YACF;YAEA,SAAS;QACX;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,WAAW,EACX,KAAK,EACL,SAAS,EACT,QAAQ,EACR,WAAW,EACX,KAAK,EACL,KAAK,EACL,QAAQ,EACR,MAAM,EACN,gBAAgB,EAChB,sBAAsB,EACtB,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,EACP,GAAG;QAEJ,aAAa;QACb,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC;YAAC;YAAO;YAAY;YAAQ;YAAQ;SAAO,CAAC,QAAQ,CAAC,YAAY;YACpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ,GAAG;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA;gBACA,OAAO,WAAW;gBAClB;gBACA;gBACA,aAAa,eAAe;gBAC5B,OAAO,SAAS;gBAChB,OAAO,SAAS;gBAChB,UAAU,YAAY;gBACtB,kBAAkB,oBAAoB;gBACtC,wBAAwB,0BAA0B;gBAClD,kBAAkB,mBAAmB,WAAW,oBAAoB;gBACpE,mBAAmB,oBAAoB,WAAW,qBAAqB;gBACvE,QAAQ,UAAU;gBAClB,QAAQ;gBACR,QAAQ;oBACN,QAAQ,QAAQ,IAAI,CAAC,OAAY,QAAkB,CAAC;4BAClD,KAAK,MAAM,GAAG;4BACd,SAAS,GAAG,MAAM,SAAS,EAAE,QAAQ,GAAG;4BACxC,WAAW,UAAU;4BACrB,OAAO;4BACP,YAAY,MAAM,UAAU,IAAI;wBAClC,CAAC,MAAM,EAAE;gBACX;YACF;YACA,SAAS;gBACP,QAAQ;gBACR,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,eAAe;4BACb,QAAQ;gCACN,cAAc;gCACd,UAAU;4BACZ;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,qBAAqB;QACrB,MAAM,QAAa;YACjB,QAAQ;QACV;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACzD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aACpD;QACH;QAEA,IAAI,UAAU;YACZ,MAAM,IAAI,GAAG;gBACX,UAAU;YACZ;QACF;QAEA,IAAI,YAAY,UAAU;YACxB,MAAM,KAAK,GAAG,CAAC;YACf,IAAI,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,WAAW;YAC3C,IAAI,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,WAAW;QAC7C;QAEA,IAAI,WAAW;YACb,MAAM,SAAS,GAAG;QACpB;QAEA,+BAA+B;QAC/B,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB;gBACA,SAAS;oBACP,QAAQ;wBACN,SAAS;4BAAE,OAAO;wBAAM;oBAC1B;oBACA,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,UAAU;4BACV,WAAW;4BACX,UAAU;4BACV,UAAU;4BACV,eAAe;gCACb,QAAQ;oCACN,cAAc;oCACd,UAAU;gCACZ;4BACF;wBACF;oBACF;gBACF;gBACA,SAAS;oBACP,WAAW;gBACb;gBACA;gBACA,MAAM;YACR;YACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC9B;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;gBACzB,SAAS,OAAO,QAAQ;YAC1B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}