'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  Brain, 
  DollarSign, 
  TrendingUp, 
  Activity, 
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  RefreshCw
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { QuickStats } from '@/components/dashboard/StatsCard'
import ChartCard, { PlaceholderChart, ChartActions } from '@/components/dashboard/ChartCard'

interface AIMetrics {
  totalRequests: number
  totalCost: number
  averageCost: number
  successRate: number
  averageResponseTime: number
  monthlyGrowth: number
  topModels: Array<{
    name: string
    requests: number
    cost: number
    successRate: number
  }>
  recentActivity: Array<{
    id: string
    type: string
    model: string
    cost: number
    responseTime: number
    status: 'success' | 'error' | 'timeout'
    timestamp: string
    userId: string
  }>
  costBreakdown: Array<{
    category: string
    cost: number
    percentage: number
  }>
}

export default function AdminAI() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [metrics, setMetrics] = useState<AIMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/login?message=Please sign in to access admin area&redirect=/admin/ai')
      return
    }

    if (session.user.userType !== 'ADMIN') {
      router.push('/?message=Access denied. Admin privileges required.')
      return
    }

    fetchAIMetrics()
  }, [session, status, router, timeRange])

  const fetchAIMetrics = async () => {
    try {
      // Mock data for demonstration
      const mockMetrics: AIMetrics = {
        totalRequests: 12450,
        totalCost: 847.32,
        averageCost: 0.068,
        successRate: 98.5,
        averageResponseTime: 1.2,
        monthlyGrowth: 23.5,
        topModels: [
          { name: 'GPT-4', requests: 5200, cost: 425.60, successRate: 99.1 },
          { name: 'GPT-3.5-turbo', requests: 4800, cost: 156.80, successRate: 98.8 },
          { name: 'DALL-E 3', requests: 1850, cost: 185.00, successRate: 97.2 },
          { name: 'Claude-3', requests: 600, cost: 79.92, successRate: 99.5 }
        ],
        recentActivity: [
          {
            id: '1',
            type: 'image_analysis',
            model: 'GPT-4V',
            cost: 0.12,
            responseTime: 2.1,
            status: 'success',
            timestamp: '2024-01-20T14:30:00Z',
            userId: 'user123'
          },
          {
            id: '2',
            type: 'listing_generation',
            model: 'GPT-3.5-turbo',
            cost: 0.03,
            responseTime: 0.8,
            status: 'success',
            timestamp: '2024-01-20T14:28:00Z',
            userId: 'user456'
          },
          {
            id: '3',
            type: 'price_suggestion',
            model: 'GPT-4',
            cost: 0.08,
            responseTime: 1.5,
            status: 'error',
            timestamp: '2024-01-20T14:25:00Z',
            userId: 'user789'
          }
        ],
        costBreakdown: [
          { category: 'Image Analysis', cost: 425.60, percentage: 50.2 },
          { category: 'Text Generation', cost: 234.80, percentage: 27.7 },
          { category: 'Price Suggestions', cost: 123.45, percentage: 14.6 },
          { category: 'Category Detection', cost: 63.47, percentage: 7.5 }
        ]
      }
      setMetrics(mockMetrics)
    } catch (error) {
      console.error('Failed to fetch AI metrics:', error)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout userType="ADMIN">
        <div className="p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!session || session.user.userType !== 'ADMIN' || !metrics) {
    return null
  }

  const quickStats = [
    {
      title: 'Total Cost',
      value: `$${metrics.totalCost.toFixed(2)}`,
      icon: <DollarSign className="w-6 h-6" />,
      color: 'green' as const,
      change: {
        value: metrics.monthlyGrowth,
        type: 'increase' as const,
        period: 'this month'
      }
    },
    {
      title: 'Total Requests',
      value: metrics.totalRequests.toLocaleString(),
      icon: <Activity className="w-6 h-6" />,
      color: 'blue' as const,
      subtitle: `$${metrics.averageCost.toFixed(3)} avg cost`
    },
    {
      title: 'Success Rate',
      value: `${metrics.successRate}%`,
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'green' as const,
      subtitle: 'API reliability'
    },
    {
      title: 'Avg Response Time',
      value: `${metrics.averageResponseTime}s`,
      icon: <Zap className="w-6 h-6" />,
      color: 'purple' as const,
      subtitle: 'Performance metric'
    }
  ]

  return (
    <DashboardLayout userType="ADMIN">
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Monitoring 🤖</h1>
            <p className="text-gray-600 mt-2">
              Monitor AI usage, costs, and performance
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
            </select>
            <button
              onClick={fetchAIMetrics}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mb-8">
          <QuickStats stats={quickStats} loading={loading} />
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Cost Trends */}
          <ChartCard
            title="AI Cost Trends"
            subtitle="Daily AI spending over time"
            actions={<ChartActions />}
          >
            <PlaceholderChart 
              type="line" 
              title="Cost Chart" 
              description="Track AI spending patterns"
            />
          </ChartCard>

          {/* Request Volume */}
          <ChartCard
            title="Request Volume"
            subtitle="AI requests by type"
            actions={<ChartActions />}
          >
            <PlaceholderChart 
              type="bar" 
              title="Volume Chart" 
              description="Monitor AI request patterns"
            />
          </ChartCard>
        </div>

        {/* Detailed Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Top Models */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Top AI Models</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {metrics.topModels.map((model, index) => (
                  <div key={model.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{model.name}</h4>
                        <p className="text-sm text-gray-600">
                          {model.requests.toLocaleString()} requests • {model.successRate}% success
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">${model.cost.toFixed(2)}</p>
                      <p className="text-sm text-gray-500">Total cost</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Cost Breakdown */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Cost Breakdown</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {metrics.costBreakdown.map((item) => (
                  <div key={item.category} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="font-medium text-gray-900">{item.category}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">${item.cost.toFixed(2)}</span>
                      <span className="text-sm font-medium text-gray-900">{item.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900">Recent AI Activity</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Model
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cost
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Response Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {metrics.recentActivity.map((activity) => (
                  <tr key={activity.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Brain className="w-4 h-4 text-blue-500 mr-2" />
                        <span className="text-sm font-medium text-gray-900">
                          {activity.type.replace('_', ' ')}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {activity.model}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${activity.cost.toFixed(3)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {activity.responseTime}s
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        activity.status === 'success' ? 'bg-green-100 text-green-800' :
                        activity.status === 'error' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {activity.status === 'success' && <CheckCircle className="w-3 h-3 mr-1" />}
                        {activity.status === 'error' && <AlertTriangle className="w-3 h-3 mr-1" />}
                        {activity.status === 'timeout' && <Clock className="w-3 h-3 mr-1" />}
                        {activity.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(activity.timestamp).toLocaleTimeString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
