'use client'

import { useState } from 'react'

export default function TestAI() {
  const [imageUrl, setImageUrl] = useState('')
  const [uploadedImage, setUploadedImage] = useState<File | null>(null)
  const [uploadedImageUrl, setUploadedImageUrl] = useState('')
  const [analysis, setAnalysis] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [testMode, setTestMode] = useState<'url' | 'upload'>('upload')

  const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    })

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.error || 'Upload failed')
    }

    return result.data.url
  }

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file')
      return
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('Image must be smaller than 10MB')
      return
    }

    setUploading(true)
    setError(null)

    try {
      const url = await uploadImage(file)
      setUploadedImage(file)
      setUploadedImageUrl(url)
      setError(null)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }

  const testImageAnalysis = async () => {
    const currentImageUrl = testMode === 'upload' ? uploadedImageUrl : imageUrl

    if (!currentImageUrl) {
      setError(testMode === 'upload' ? 'Please upload an image first' : 'Please enter an image URL')
      return
    }

    setLoading(true)
    setError(null)
    setAnalysis(null)

    try {
      const response = await fetch('/api/ai/analyze-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ imageUrl: currentImageUrl })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Analysis failed')
      }

      setAnalysis(result.analysis)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Analysis failed')
    } finally {
      setLoading(false)
    }
  }

  const testPriceSuggestion = async () => {
    if (!analysis) {
      setError('Please analyze an image first')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/ai/suggest-price', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productInfo: {
            category: analysis.category,
            subcategory: analysis.subcategory,
            brand: analysis.brand,
            model: analysis.model,
            condition: analysis.condition,
            description: analysis.description
          }
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Price suggestion failed')
      }

      setAnalysis(prev => ({
        ...prev,
        priceSuggestion: result.priceSuggestion
      }))
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Price suggestion failed')
    } finally {
      setLoading(false)
    }
  }

  const clearAll = () => {
    setImageUrl('')
    setUploadedImage(null)
    setUploadedImageUrl('')
    setAnalysis(null)
    setError(null)
  }

  const testImageUrl = async () => {
    const currentImageUrl = testMode === 'upload' ? uploadedImageUrl : imageUrl

    if (!currentImageUrl) {
      setError('No image URL to test')
      return
    }

    try {
      const response = await fetch('/api/test-image-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ imageUrl: currentImageUrl })
      })

      const result = await response.json()
      console.log('Image URL test result:', result)

      if (result.accessible) {
        setError(null)
        alert(`✅ Image URL is accessible!\nStatus: ${result.status}\nContent-Type: ${result.contentType}`)
      } else {
        setError(`❌ Image URL not accessible: ${result.status} ${result.statusText}`)
      }
    } catch (error) {
      setError('Failed to test image URL')
      console.error('URL test error:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold text-gray-900">
              🤖 AI Integration Test
            </h1>
            <button
              onClick={clearAll}
              className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              🗑️ Clear All
            </button>
          </div>
          
          <div className="space-y-6">
            {/* Mode Switcher */}
            <div className="flex space-x-4 mb-6">
              <button
                onClick={() => setTestMode('upload')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  testMode === 'upload'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                📁 Upload Image
              </button>
              <button
                onClick={() => setTestMode('url')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  testMode === 'url'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                🔗 Use Image URL
              </button>
            </div>

            {/* Image Upload Section */}
            {testMode === 'upload' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload Product Image
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                    disabled={uploading}
                  />
                  <label
                    htmlFor="image-upload"
                    className={`cursor-pointer ${uploading ? 'cursor-not-allowed opacity-50' : ''}`}
                  >
                    {uploading ? (
                      <div className="space-y-2">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                        <p className="text-gray-600">Uploading...</p>
                      </div>
                    ) : uploadedImage ? (
                      <div className="space-y-2">
                        <div className="text-4xl">✅</div>
                        <p className="text-green-600 font-medium">Image uploaded successfully!</p>
                        <p className="text-sm text-gray-500">{uploadedImage.name}</p>
                        <p className="text-xs text-gray-400">
                          {(uploadedImage.size / 1024).toFixed(1)}KB
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div className="text-4xl">📸</div>
                        <p className="text-lg font-medium">Click to upload an image</p>
                        <p className="text-gray-500">
                          Supports JPG, PNG, WebP (max 10MB)
                        </p>
                      </div>
                    )}
                  </label>
                </div>
                {uploadedImageUrl && (
                  <div className="mt-4 space-y-2">
                    <button
                      onClick={testImageUrl}
                      className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 font-medium"
                    >
                      🔍 Test Image URL
                    </button>
                    <button
                      onClick={testImageAnalysis}
                      disabled={loading}
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
                    >
                      {loading ? '🤖 Analyzing Image...' : '🤖 Analyze with AI'}
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Image URL Input */}
            {testMode === 'url' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Test Image URL
                </label>
                <div className="flex space-x-2">
                  <input
                    type="url"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    placeholder="https://example.com/image.jpg"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    onClick={testImageAnalysis}
                    disabled={loading || !imageUrl}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Analyzing...' : 'Analyze Image'}
                  </button>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Try with a product image URL (e.g., from Google Images)
                </p>
              </div>
            )}

            {/* Sample URLs - Only show in URL mode */}
            {testMode === 'url' && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Sample Test URLs:</h3>
                <div className="space-y-1">
                  <button
                    onClick={() => setImageUrl('https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500')}
                    className="block text-sm text-blue-600 hover:underline"
                  >
                    📱 Smartphone
                  </button>
                  <button
                    onClick={() => setImageUrl('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500')}
                    className="block text-sm text-blue-600 hover:underline"
                  >
                    🪑 Chair
                  </button>
                  <button
                    onClick={() => setImageUrl('https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500')}
                    className="block text-sm text-blue-600 hover:underline"
                  >
                    👟 Sneakers
                  </button>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {/* Analysis Results */}
            {analysis && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-green-800">
                    🎯 Analysis Results
                  </h3>
                  <span className="text-sm text-green-600">
                    Confidence: {Math.round(analysis.confidence * 100)}%
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <strong>Title:</strong>
                    <p className="text-gray-700">{analysis.title}</p>
                  </div>
                  <div>
                    <strong>Category:</strong>
                    <p className="text-gray-700">{analysis.category}</p>
                  </div>
                  <div>
                    <strong>Condition:</strong>
                    <p className="text-gray-700">{analysis.condition}</p>
                  </div>
                  {analysis.brand && (
                    <div>
                      <strong>Brand:</strong>
                      <p className="text-gray-700">{analysis.brand}</p>
                    </div>
                  )}
                  {analysis.model && (
                    <div>
                      <strong>Model:</strong>
                      <p className="text-gray-700">{analysis.model}</p>
                    </div>
                  )}
                  {analysis.subcategory && (
                    <div>
                      <strong>Subcategory:</strong>
                      <p className="text-gray-700">{analysis.subcategory}</p>
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <strong>Description:</strong>
                  <p className="text-gray-700 mt-1">{analysis.description}</p>
                </div>

                {analysis.tags && analysis.tags.length > 0 && (
                  <div className="mb-4">
                    <strong>Tags:</strong>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {analysis.tags.map((tag: string, index: number) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Price Suggestion */}
                {analysis.priceSuggestion ? (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-800 mb-2">💰 Price Suggestion</h4>
                    <div className="text-sm">
                      <p><strong>Suggested Price:</strong> R{analysis.priceSuggestion.suggestedPrice}</p>
                      <p><strong>Price Range:</strong> R{analysis.priceSuggestion.priceRange.min} - R{analysis.priceSuggestion.priceRange.max}</p>
                      <p><strong>Confidence:</strong> {Math.round(analysis.priceSuggestion.confidence * 100)}%</p>
                      <p className="mt-2"><strong>Reasoning:</strong> {analysis.priceSuggestion.reasoning}</p>
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={testPriceSuggestion}
                    disabled={loading}
                    className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 disabled:opacity-50"
                  >
                    {loading ? 'Getting Price...' : 'Get Price Suggestion'}
                  </button>
                )}
              </div>
            )}

            {/* Image Preview */}
            {(testMode === 'url' && imageUrl) || (testMode === 'upload' && uploadedImageUrl) ? (
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Image Preview:</h3>
                <div className="flex justify-center">
                  <img
                    src={testMode === 'upload' ? uploadedImageUrl : imageUrl}
                    alt="Test image"
                    className="max-w-full h-auto max-h-64 rounded-lg shadow-md"
                    onError={() => setError('Failed to load image. Please check the image.')}
                  />
                </div>
                {testMode === 'upload' && uploadedImage && (
                  <div className="mt-2 text-center text-sm text-gray-500">
                    <p>{uploadedImage.name} • {(uploadedImage.size / 1024).toFixed(1)}KB</p>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  )
}
