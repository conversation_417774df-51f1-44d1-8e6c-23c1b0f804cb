'use client'

import { useState } from 'react'

export default function TestAI() {
  const [imageUrl, setImageUrl] = useState('')
  const [analysis, setAnalysis] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testImageAnalysis = async () => {
    if (!imageUrl) {
      setError('Please enter an image URL')
      return
    }

    setLoading(true)
    setError(null)
    setAnalysis(null)

    try {
      const response = await fetch('/api/ai/analyze-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ imageUrl })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Analysis failed')
      }

      setAnalysis(result.analysis)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Analysis failed')
    } finally {
      setLoading(false)
    }
  }

  const testPriceSuggestion = async () => {
    if (!analysis) {
      setError('Please analyze an image first')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/ai/suggest-price', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productInfo: {
            category: analysis.category,
            subcategory: analysis.subcategory,
            brand: analysis.brand,
            model: analysis.model,
            condition: analysis.condition,
            description: analysis.description
          }
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Price suggestion failed')
      }

      setAnalysis(prev => ({
        ...prev,
        priceSuggestion: result.priceSuggestion
      }))
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Price suggestion failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🤖 AI Integration Test
          </h1>
          
          <div className="space-y-6">
            {/* Image URL Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Test Image URL
              </label>
              <div className="flex space-x-2">
                <input
                  type="url"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  onClick={testImageAnalysis}
                  disabled={loading || !imageUrl}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Analyzing...' : 'Analyze Image'}
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Try with a product image URL (e.g., from Google Images)
              </p>
            </div>

            {/* Sample URLs */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Sample Test URLs:</h3>
              <div className="space-y-1">
                <button
                  onClick={() => setImageUrl('https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500')}
                  className="block text-sm text-blue-600 hover:underline"
                >
                  📱 Smartphone
                </button>
                <button
                  onClick={() => setImageUrl('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500')}
                  className="block text-sm text-blue-600 hover:underline"
                >
                  🪑 Chair
                </button>
                <button
                  onClick={() => setImageUrl('https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500')}
                  className="block text-sm text-blue-600 hover:underline"
                >
                  👟 Sneakers
                </button>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {/* Analysis Results */}
            {analysis && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-green-800">
                    🎯 Analysis Results
                  </h3>
                  <span className="text-sm text-green-600">
                    Confidence: {Math.round(analysis.confidence * 100)}%
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <strong>Title:</strong>
                    <p className="text-gray-700">{analysis.title}</p>
                  </div>
                  <div>
                    <strong>Category:</strong>
                    <p className="text-gray-700">{analysis.category}</p>
                  </div>
                  <div>
                    <strong>Condition:</strong>
                    <p className="text-gray-700">{analysis.condition}</p>
                  </div>
                  {analysis.brand && (
                    <div>
                      <strong>Brand:</strong>
                      <p className="text-gray-700">{analysis.brand}</p>
                    </div>
                  )}
                  {analysis.model && (
                    <div>
                      <strong>Model:</strong>
                      <p className="text-gray-700">{analysis.model}</p>
                    </div>
                  )}
                  {analysis.subcategory && (
                    <div>
                      <strong>Subcategory:</strong>
                      <p className="text-gray-700">{analysis.subcategory}</p>
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <strong>Description:</strong>
                  <p className="text-gray-700 mt-1">{analysis.description}</p>
                </div>

                {analysis.tags && analysis.tags.length > 0 && (
                  <div className="mb-4">
                    <strong>Tags:</strong>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {analysis.tags.map((tag: string, index: number) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Price Suggestion */}
                {analysis.priceSuggestion ? (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-800 mb-2">💰 Price Suggestion</h4>
                    <div className="text-sm">
                      <p><strong>Suggested Price:</strong> R{analysis.priceSuggestion.suggestedPrice}</p>
                      <p><strong>Price Range:</strong> R{analysis.priceSuggestion.priceRange.min} - R{analysis.priceSuggestion.priceRange.max}</p>
                      <p><strong>Confidence:</strong> {Math.round(analysis.priceSuggestion.confidence * 100)}%</p>
                      <p className="mt-2"><strong>Reasoning:</strong> {analysis.priceSuggestion.reasoning}</p>
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={testPriceSuggestion}
                    disabled={loading}
                    className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 disabled:opacity-50"
                  >
                    {loading ? 'Getting Price...' : 'Get Price Suggestion'}
                  </button>
                )}
              </div>
            )}

            {/* Image Preview */}
            {imageUrl && (
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Image Preview:</h3>
                <img
                  src={imageUrl}
                  alt="Test image"
                  className="max-w-full h-auto max-h-64 rounded-lg"
                  onError={() => setError('Failed to load image. Please check the URL.')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
