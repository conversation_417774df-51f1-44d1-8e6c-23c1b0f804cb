{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/LoginForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { signIn } from 'next-auth/react'\nimport { useRouter, useSearchParams } from 'next/navigation'\nimport Link from 'next/link'\n\nexport default function LoginForm() {\n  const router = useRouter()\n  const searchParams = useSearchParams()\n  const [message, setMessage] = useState<string | null>(null)\n  const [redirectUrl, setRedirectUrl] = useState<string | null>(null)\n\n  // Handle search params on client side to avoid hydration issues\n  useEffect(() => {\n    if (searchParams) {\n      setMessage(searchParams.get('message'))\n      setRedirectUrl(searchParams.get('redirect'))\n    }\n  }, [searchParams])\n\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  })\n\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    setError(null)\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!formData.email || !formData.password) {\n      setError('Please fill in all fields')\n      return\n    }\n\n    setIsSubmitting(true)\n    setError(null)\n\n    try {\n      const result = await signIn('credentials', {\n        email: formData.email,\n        password: formData.password,\n        redirect: false\n      })\n\n      if (result?.error) {\n        setError('Invalid email or password')\n      } else {\n        // Successful login - redirect to intended page or home\n        const destination = redirectUrl || '/'\n        router.push(destination)\n        router.refresh()\n      }\n    } catch (error) {\n      setError('Login failed. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-lg p-8\">\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Welcome Back\n        </h1>\n        <p className=\"text-gray-600\">\n          Sign in to your Family Marketplace account\n        </p>\n      </div>\n\n      {/* Success message from registration */}\n      {message && (\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-3 mb-6\">\n          <p className=\"text-green-700 text-sm\">{message}</p>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Email Address\n          </label>\n          <input\n            type=\"email\"\n            required\n            value={formData.email}\n            onChange={(e) => handleInputChange('email', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"<EMAIL>\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Password\n          </label>\n          <input\n            type=\"password\"\n            required\n            value={formData.password}\n            onChange={(e) => handleInputChange('password', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"••••••••\"\n          />\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n            <p className=\"text-red-700 text-sm\">{error}</p>\n          </div>\n        )}\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          disabled={isSubmitting}\n          className={`w-full py-3 px-4 rounded-md font-semibold text-white transition-all bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 ${\n            isSubmitting ? 'opacity-50 cursor-not-allowed' : ''\n          }`}\n        >\n          {isSubmitting ? (\n            <span className=\"flex items-center justify-center\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              Signing In...\n            </span>\n          ) : (\n            'Sign In'\n          )}\n        </button>\n\n        {/* Forgot Password */}\n        <div className=\"text-center\">\n          <Link href=\"/forgot-password\" className=\"text-sm text-blue-600 hover:underline\">\n            Forgot your password?\n          </Link>\n        </div>\n\n        {/* Divider */}\n        <div className=\"relative\">\n          <div className=\"absolute inset-0 flex items-center\">\n            <div className=\"w-full border-t border-gray-300\" />\n          </div>\n          <div className=\"relative flex justify-center text-sm\">\n            <span className=\"px-2 bg-white text-gray-500\">Don't have an account?</span>\n          </div>\n        </div>\n\n        {/* Registration Links */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <Link\n            href=\"/register?type=private\"\n            className=\"flex items-center justify-center px-4 py-2 border border-blue-300 rounded-md text-blue-700 hover:bg-blue-50 transition-all hover:border-blue-400\"\n          >\n            <span className=\"mr-2\">👤</span>\n            Join as Private Seller\n          </Link>\n\n          <Link\n            href=\"/register?type=vendor\"\n            className=\"flex items-center justify-center px-4 py-2 border border-purple-300 rounded-md text-purple-700 hover:bg-purple-50 transition-all hover:border-purple-400\"\n          >\n            <span className=\"mr-2\">🏪</span>\n            Join as Vendor\n          </Link>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,WAAW,aAAa,GAAG,CAAC;YAC5B,eAAe,aAAa,GAAG,CAAC;QAClC;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,SAAS;IACX;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YACzC,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX,OAAO;gBACL,uDAAuD;gBACvD,MAAM,cAAc,eAAe;gBACnC,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAM9B,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAA0B;;;;;;;;;;;0BAI3C,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,QAAQ;gCACR,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,QAAQ;gCACR,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC7D,WAAU;gCACV,aAAY;;;;;;;;;;;;oBAKf,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAKzC,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAW,CAAC,qJAAqJ,EAC/J,eAAe,kCAAkC,IACjD;kCAED,6BACC,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;;;;;gCAAuE;;;;;;mCAIxF;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAmB,WAAU;sCAAwC;;;;;;;;;;;kCAMlF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA8B;;;;;;;;;;;;;;;;;kCAKlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAO;;;;;;oCAAS;;;;;;;0CAIlC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAO;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}]}