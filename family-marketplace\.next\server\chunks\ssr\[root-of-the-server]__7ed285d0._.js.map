{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport Image from 'next/image'\nimport { UploadResult } from '@/types'\n\n// Define interfaces locally to avoid import issues\ninterface AIAnalysisResult {\n  category: string\n  subcategory?: string\n  title: string\n  description: string\n  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'\n  brand?: string\n  model?: string\n  tags: string[]\n  confidence: number\n}\n\ninterface ImageUploadProps {\n  onUpload: (result: UploadResult) => void\n  onAnalysis?: (analysis: AIAnalysisResult) => void\n  maxFiles?: number\n  className?: string\n}\n\nexport default function ImageUpload({ \n  onUpload, \n  onAnalysis, \n  maxFiles = 5, \n  className = '' \n}: ImageUploadProps) {\n  const [uploading, setUploading] = useState(false)\n  const [analyzing, setAnalyzing] = useState(false)\n  const [uploadedImages, setUploadedImages] = useState<UploadResult[]>([])\n  const [error, setError] = useState<string | null>(null)\n\n  const uploadImage = async (file: File): Promise<UploadResult> => {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const response = await fetch('/api/upload', {\n      method: 'POST',\n      body: formData\n    })\n\n    const result = await response.json()\n    \n    if (!result.success) {\n      throw new Error(result.error || 'Upload failed')\n    }\n\n    return result.data\n  }\n\n  const analyzeImage = async (imageUrl: string): Promise<AIAnalysisResult> => {\n    const response = await fetch('/api/ai/analyze', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({ imageUrl, type: 'image' })\n    })\n\n    const result = await response.json()\n    \n    if (!result.success) {\n      throw new Error(result.error || 'Analysis failed')\n    }\n\n    return result.data\n  }\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (uploadedImages.length + acceptedFiles.length > maxFiles) {\n      setError(`Maximum ${maxFiles} images allowed`)\n      return\n    }\n\n    setUploading(true)\n    setError(null)\n\n    try {\n      const uploadPromises = acceptedFiles.map(uploadImage)\n      const uploadResults = await Promise.all(uploadPromises)\n      \n      setUploadedImages(prev => [...prev, ...uploadResults])\n      \n      // Notify parent component\n      uploadResults.forEach(result => onUpload(result))\n\n      // Analyze the first uploaded image if analysis callback is provided\n      if (onAnalysis && uploadResults.length > 0) {\n        setAnalyzing(true)\n        try {\n          const analysis = await analyzeImage(uploadResults[0].url)\n          onAnalysis(analysis)\n        } catch (analysisError) {\n          console.error('Analysis failed:', analysisError)\n          // Don't show error for analysis failure - it's optional\n        } finally {\n          setAnalyzing(false)\n        }\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Upload failed')\n    } finally {\n      setUploading(false)\n    }\n  }, [uploadedImages.length, maxFiles, onUpload, onAnalysis])\n\n  const removeImage = async (index: number) => {\n    const image = uploadedImages[index]\n    \n    try {\n      // Delete from server\n      await fetch(`/api/upload?filename=${image.filename}`, {\n        method: 'DELETE'\n      })\n      \n      // Remove from state\n      setUploadedImages(prev => prev.filter((_, i) => i !== index))\n    } catch (error) {\n      console.error('Failed to delete image:', error)\n    }\n  }\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.webp']\n    },\n    maxFiles: maxFiles - uploadedImages.length,\n    disabled: uploading || uploadedImages.length >= maxFiles\n  })\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Upload Area */}\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}\n          ${uploading ? 'opacity-50 cursor-not-allowed' : ''}\n          ${uploadedImages.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : ''}\n        `}\n      >\n        <input {...getInputProps()} />\n        \n        {uploading ? (\n          <div className=\"space-y-2\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"></div>\n            <p className=\"text-gray-600\">Uploading images...</p>\n          </div>\n        ) : analyzing ? (\n          <div className=\"space-y-2\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mx-auto\"></div>\n            </div>\n            <p className=\"text-gray-600\">🤖 AI is analyzing your image...</p>\n            <p className=\"text-sm text-gray-500\">This will help suggest category, title, and description</p>\n          </div>\n        ) : uploadedImages.length >= maxFiles ? (\n          <p className=\"text-gray-500\">Maximum {maxFiles} images uploaded</p>\n        ) : (\n          <div className=\"space-y-2\">\n            <div className=\"text-4xl\">📸</div>\n            <p className=\"text-lg font-medium\">\n              {isDragActive ? 'Drop images here' : 'Upload product images'}\n            </p>\n            <p className=\"text-gray-500\">\n              Drag & drop or click to select ({uploadedImages.length}/{maxFiles})\n            </p>\n            <p className=\"text-sm text-blue-600\">\n              ✨ AI will automatically analyze your images to suggest details\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n          <p className=\"text-red-700 text-sm\">{error}</p>\n        </div>\n      )}\n\n      {/* Uploaded Images */}\n      {uploadedImages.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\">\n          {uploadedImages.map((image, index) => (\n            <div key={image.filename} className=\"relative group\">\n              <div className=\"aspect-square relative rounded-lg overflow-hidden border border-gray-200\">\n                <Image\n                  src={image.url}\n                  alt={`Upload ${index + 1}`}\n                  fill\n                  className=\"object-cover\"\n                />\n                <button\n                  onClick={() => removeImage(index)}\n                  className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity\"\n                >\n                  ×\n                </button>\n              </div>\n              <p className=\"text-xs text-gray-500 mt-1 truncate\">\n                {(image.size / 1024).toFixed(1)}KB\n              </p>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AA2Be,SAAS,YAAY,EAClC,QAAQ,EACR,UAAU,EACV,WAAW,CAAC,EACZ,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,MAAM,eAAe;YAC1C,QAAQ;YACR,MAAM;QACR;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,WAAW,MAAM,MAAM,mBAAmB;YAC9C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAU,MAAM;YAAQ;QACjD;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,IAAI,eAAe,MAAM,GAAG,cAAc,MAAM,GAAG,UAAU;YAC3D,SAAS,CAAC,QAAQ,EAAE,SAAS,eAAe,CAAC;YAC7C;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,iBAAiB,cAAc,GAAG,CAAC;YACzC,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CAAC;YAExC,kBAAkB,CAAA,OAAQ;uBAAI;uBAAS;iBAAc;YAErD,0BAA0B;YAC1B,cAAc,OAAO,CAAC,CAAA,SAAU,SAAS;YAEzC,oEAAoE;YACpE,IAAI,cAAc,cAAc,MAAM,GAAG,GAAG;gBAC1C,aAAa;gBACb,IAAI;oBACF,MAAM,WAAW,MAAM,aAAa,aAAa,CAAC,EAAE,CAAC,GAAG;oBACxD,WAAW;gBACb,EAAE,OAAO,eAAe;oBACtB,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,wDAAwD;gBAC1D,SAAU;oBACR,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC,eAAe,MAAM;QAAE;QAAU;QAAU;KAAW;IAE1D,MAAM,cAAc,OAAO;QACzB,MAAM,QAAQ,cAAc,CAAC,MAAM;QAEnC,IAAI;YACF,qBAAqB;YACrB,MAAM,MAAM,CAAC,qBAAqB,EAAE,MAAM,QAAQ,EAAE,EAAE;gBACpD,QAAQ;YACV;YAEA,oBAAoB;YACpB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;aAAQ;QAC/C;QACA,UAAU,WAAW,eAAe,MAAM;QAC1C,UAAU,aAAa,eAAe,MAAM,IAAI;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eAAe,+BAA+B,wCAAwC;UACxF,EAAE,YAAY,kCAAkC,GAAG;UACnD,EAAE,eAAe,MAAM,IAAI,WAAW,kCAAkC,GAAG;QAC7E,CAAC;;kCAED,8OAAC;wBAAO,GAAG,eAAe;;;;;;oBAEzB,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,0BACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;+BAErC,eAAe,MAAM,IAAI,yBAC3B,8OAAC;wBAAE,WAAU;;4BAAgB;4BAAS;4BAAS;;;;;;6CAE/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAW;;;;;;0CAC1B,8OAAC;gCAAE,WAAU;0CACV,eAAe,qBAAqB;;;;;;0CAEvC,8OAAC;gCAAE,WAAU;;oCAAgB;oCACM,eAAe,MAAM;oCAAC;oCAAE;oCAAS;;;;;;;0CAEpE,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAQ1C,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;YAKxC,eAAe,MAAM,GAAG,mBACvB,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;wBAAyB,WAAU;;0CAClC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,MAAM,GAAG;wCACd,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG;wCAC1B,IAAI;wCACJ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC;gCAAE,WAAU;;oCACV,CAAC,MAAM,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;;uBAhB1B,MAAM,QAAQ;;;;;;;;;;;;;;;;AAwBpC", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/CreateListingForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport ImageUpload from './ImageUpload'\nimport { UploadResult } from '@/types'\n\n// Local interfaces to avoid import issues\ninterface AIAnalysisResult {\n  category: string\n  subcategory?: string\n  title: string\n  description: string\n  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'\n  brand?: string\n  model?: string\n  tags: string[]\n  confidence: number\n}\n\ninterface PriceSuggestion {\n  suggestedPrice: number\n  priceRange: { min: number; max: number }\n  confidence: number\n  reasoning: string\n}\n\ninterface FormData {\n  title: string\n  description: string\n  price: number\n  condition: string\n  category: string\n  subcategory: string\n  brand: string\n  model: string\n  location: string\n  images: UploadResult[]\n}\n\nconst CATEGORIES = [\n  'Electronics',\n  'Furniture',\n  'Clothing',\n  'Books',\n  'Sports & Outdoors',\n  'Home & Garden',\n  'Toys & Games',\n  'Automotive',\n  'Health & Beauty',\n  'Other'\n]\n\nconst CONDITIONS = [\n  { value: 'NEW', label: 'New' },\n  { value: 'LIKE_NEW', label: 'Like New' },\n  { value: 'GOOD', label: 'Good' },\n  { value: 'FAIR', label: 'Fair' },\n  { value: 'POOR', label: 'Poor' }\n]\n\nexport default function CreateListingForm() {\n  const [formData, setFormData] = useState<FormData>({\n    title: '',\n    description: '',\n    price: 0,\n    condition: 'GOOD',\n    category: '',\n    subcategory: '',\n    brand: '',\n    model: '',\n    location: '',\n    images: []\n  })\n\n  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysisResult | null>(null)\n  const [priceSuggestion, setPriceSuggestion] = useState<PriceSuggestion | null>(null)\n  const [isLoadingPrice, setIsLoadingPrice] = useState(false)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [showAiSuggestions, setShowAiSuggestions] = useState(true)\n\n  // Handle AI analysis results\n  const handleAiAnalysis = (analysis: AIAnalysisResult) => {\n    setAiAnalysis(analysis)\n    \n    // Auto-fill form with AI suggestions if user wants\n    if (showAiSuggestions) {\n      setFormData(prev => ({\n        ...prev,\n        title: analysis.title || prev.title,\n        description: analysis.description || prev.description,\n        category: analysis.category || prev.category,\n        subcategory: analysis.subcategory || prev.subcategory,\n        brand: analysis.brand || prev.brand,\n        model: analysis.model || prev.model,\n        condition: analysis.condition || prev.condition\n      }))\n    }\n  }\n\n  // Get price suggestion when enough data is available\n  useEffect(() => {\n    const getPriceSuggestion = async () => {\n      if (!formData.category || !formData.condition || !formData.description) {\n        return\n      }\n\n      setIsLoadingPrice(true)\n      try {\n        const response = await fetch('/api/ai/price', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({\n            category: formData.category,\n            subcategory: formData.subcategory,\n            brand: formData.brand,\n            model: formData.model,\n            condition: formData.condition,\n            description: formData.description\n          })\n        })\n\n        const result = await response.json()\n        if (result.success) {\n          setPriceSuggestion(result.data)\n          \n          // Auto-fill price if user hasn't set one\n          if (formData.price === 0 && showAiSuggestions) {\n            setFormData(prev => ({\n              ...prev,\n              price: result.data.suggestedPrice\n            }))\n          }\n        }\n      } catch (error) {\n        console.error('Price suggestion failed:', error)\n      } finally {\n        setIsLoadingPrice(false)\n      }\n    }\n\n    // Debounce the price suggestion\n    const timer = setTimeout(getPriceSuggestion, 1000)\n    return () => clearTimeout(timer)\n  }, [formData.category, formData.condition, formData.description, formData.brand, formData.model, formData.subcategory])\n\n  const handleInputChange = (field: keyof FormData, value: string | number) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const handleImageUpload = (result: UploadResult) => {\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, result]\n    }))\n  }\n\n  const applySuggestion = (field: keyof FormData, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n\n    try {\n      // Here you would submit to your API\n      console.log('Submitting listing:', formData)\n      \n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      alert('Listing created successfully!')\n      \n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        price: 0,\n        condition: 'GOOD',\n        category: '',\n        subcategory: '',\n        brand: '',\n        model: '',\n        location: '',\n        images: []\n      })\n      setAiAnalysis(null)\n      setPriceSuggestion(null)\n    } catch (error) {\n      console.error('Submission failed:', error)\n      alert('Failed to create listing. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Create New Listing\n        </h1>\n        <p className=\"text-gray-600\">\n          🤖 Upload photos and let AI help you create the perfect listing\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Image Upload Section */}\n        <div className=\"space-y-4\">\n          <h2 className=\"text-xl font-semibold\">Product Images</h2>\n          <ImageUpload\n            onUpload={handleImageUpload}\n            onAnalysis={handleAiAnalysis}\n            maxFiles={5}\n          />\n        </div>\n\n        {/* AI Analysis Results */}\n        {aiAnalysis && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"text-2xl mr-2\">🤖</div>\n              <h3 className=\"text-lg font-semibold text-purple-800\">\n                AI Analysis Results\n              </h3>\n              <span className=\"ml-auto text-sm text-purple-600\">\n                Confidence: {Math.round(aiAnalysis.confidence * 100)}%\n              </span>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n              <div>\n                <strong>Suggested Title:</strong>\n                <p className=\"text-gray-700\">{aiAnalysis.title}</p>\n                {aiAnalysis.title !== formData.title && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('title', aiAnalysis.title)}\n                    className=\"text-blue-600 hover:underline text-xs\"\n                  >\n                    Apply suggestion\n                  </button>\n                )}\n              </div>\n              \n              <div>\n                <strong>Category:</strong>\n                <p className=\"text-gray-700\">{aiAnalysis.category}</p>\n                {aiAnalysis.category !== formData.category && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('category', aiAnalysis.category)}\n                    className=\"text-blue-600 hover:underline text-xs\"\n                  >\n                    Apply suggestion\n                  </button>\n                )}\n              </div>\n              \n              {aiAnalysis.brand && (\n                <div>\n                  <strong>Brand:</strong>\n                  <p className=\"text-gray-700\">{aiAnalysis.brand}</p>\n                  {aiAnalysis.brand !== formData.brand && (\n                    <button\n                      type=\"button\"\n                      onClick={() => applySuggestion('brand', aiAnalysis.brand)}\n                      className=\"text-blue-600 hover:underline text-xs\"\n                    >\n                      Apply suggestion\n                    </button>\n                  )}\n                </div>\n              )}\n              \n              <div>\n                <strong>Condition:</strong>\n                <p className=\"text-gray-700\">{aiAnalysis.condition}</p>\n                {aiAnalysis.condition !== formData.condition && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('condition', aiAnalysis.condition)}\n                    className=\"text-blue-600 hover:underline text-xs\"\n                  >\n                    Apply suggestion\n                  </button>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"mt-4\">\n              <strong>Suggested Description:</strong>\n              <p className=\"text-gray-700 text-sm mt-1\">{aiAnalysis.description}</p>\n              {aiAnalysis.description !== formData.description && (\n                <button\n                  type=\"button\"\n                  onClick={() => applySuggestion('description', aiAnalysis.description)}\n                  className=\"text-blue-600 hover:underline text-xs mt-1\"\n                >\n                  Apply suggestion\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Form Fields */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Title */}\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.title}\n              onChange={(e) => handleInputChange('title', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Enter a descriptive title for your item\"\n            />\n          </div>\n\n          {/* Category */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Category *\n            </label>\n            <select\n              required\n              value={formData.category}\n              onChange={(e) => handleInputChange('category', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">Select a category</option>\n              {CATEGORIES.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Condition */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Condition *\n            </label>\n            <select\n              required\n              value={formData.condition}\n              onChange={(e) => handleInputChange('condition', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {CONDITIONS.map(condition => (\n                <option key={condition.value} value={condition.value}>\n                  {condition.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Brand */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Brand\n            </label>\n            <input\n              type=\"text\"\n              value={formData.brand}\n              onChange={(e) => handleInputChange('brand', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Brand name\"\n            />\n          </div>\n\n          {/* Model */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Model\n            </label>\n            <input\n              type=\"text\"\n              value={formData.model}\n              onChange={(e) => handleInputChange('model', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Model name/number\"\n            />\n          </div>\n        </div>\n\n        {/* Price Section */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <label className=\"block text-sm font-medium text-gray-700\">\n              Price * ($)\n            </label>\n            {isLoadingPrice && (\n              <span className=\"text-sm text-blue-600\">🤖 Getting price suggestion...</span>\n            )}\n          </div>\n          \n          <input\n            type=\"number\"\n            required\n            min=\"0\"\n            step=\"0.01\"\n            value={formData.price}\n            onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"0.00\"\n          />\n\n          {/* Price Suggestion */}\n          {priceSuggestion && (\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-green-600 font-medium\">💡 AI Price Suggestion</span>\n                <span className=\"ml-auto text-sm text-green-600\">\n                  Confidence: {Math.round(priceSuggestion.confidence * 100)}%\n                </span>\n              </div>\n              <div className=\"text-sm text-green-700\">\n                <p><strong>Suggested Price:</strong> ${priceSuggestion.suggestedPrice.toFixed(2)}</p>\n                <p><strong>Price Range:</strong> ${priceSuggestion.priceRange.min.toFixed(2)} - ${priceSuggestion.priceRange.max.toFixed(2)}</p>\n                <p className=\"mt-2\"><strong>Reasoning:</strong> {priceSuggestion.reasoning}</p>\n                {priceSuggestion.suggestedPrice !== formData.price && (\n                  <button\n                    type=\"button\"\n                    onClick={() => applySuggestion('price', priceSuggestion.suggestedPrice)}\n                    className=\"text-green-600 hover:underline text-sm mt-2\"\n                  >\n                    Apply suggested price\n                  </button>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Description */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Description *\n          </label>\n          <textarea\n            required\n            rows={6}\n            value={formData.description}\n            onChange={(e) => handleInputChange('description', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"Describe your item in detail...\"\n          />\n        </div>\n\n        {/* Location */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Location *\n          </label>\n          <input\n            type=\"text\"\n            required\n            value={formData.location}\n            onChange={(e) => handleInputChange('location', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"City, State\"\n          />\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <button\n            type=\"button\"\n            className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n          >\n            Save as Draft\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isSubmitting || formData.images.length === 0}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSubmitting ? 'Creating Listing...' : 'Create Listing'}\n          </button>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAuCA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;IAAM;IAC7B;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAQ,OAAO;IAAO;CAChC;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,OAAO;QACP,aAAa;QACb,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ,EAAE;IACZ;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,6BAA6B;IAC7B,MAAM,mBAAmB,CAAC;QACxB,cAAc;QAEd,mDAAmD;QACnD,IAAI,mBAAmB;YACrB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,OAAO,SAAS,KAAK,IAAI,KAAK,KAAK;oBACnC,aAAa,SAAS,WAAW,IAAI,KAAK,WAAW;oBACrD,UAAU,SAAS,QAAQ,IAAI,KAAK,QAAQ;oBAC5C,aAAa,SAAS,WAAW,IAAI,KAAK,WAAW;oBACrD,OAAO,SAAS,KAAK,IAAI,KAAK,KAAK;oBACnC,OAAO,SAAS,KAAK,IAAI,KAAK,KAAK;oBACnC,WAAW,SAAS,SAAS,IAAI,KAAK,SAAS;gBACjD,CAAC;QACH;IACF;IAEA,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,WAAW,EAAE;gBACtE;YACF;YAEA,kBAAkB;YAClB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,UAAU,SAAS,QAAQ;wBAC3B,aAAa,SAAS,WAAW;wBACjC,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;wBACrB,WAAW,SAAS,SAAS;wBAC7B,aAAa,SAAS,WAAW;oBACnC;gBACF;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,IAAI,OAAO,OAAO,EAAE;oBAClB,mBAAmB,OAAO,IAAI;oBAE9B,yCAAyC;oBACzC,IAAI,SAAS,KAAK,KAAK,KAAK,mBAAmB;wBAC7C,YAAY,CAAA,OAAQ,CAAC;gCACnB,GAAG,IAAI;gCACP,OAAO,OAAO,IAAI,CAAC,cAAc;4BACnC,CAAC;oBACH;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,kBAAkB;YACpB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,WAAW,oBAAoB;QAC7C,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC,SAAS,QAAQ;QAAE,SAAS,SAAS;QAAE,SAAS,WAAW;QAAE,SAAS,KAAK;QAAE,SAAS,KAAK;QAAE,SAAS,WAAW;KAAC;IAEtH,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;uBAAI,KAAK,MAAM;oBAAE;iBAAO;YAClC,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC,OAAuB;QAC9C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,oCAAoC;YACpC,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM;YAEN,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,QAAQ,EAAE;YACZ;YACA,cAAc;YACd,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAK/B,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC,iIAAA,CAAA,UAAW;gCACV,UAAU;gCACV,YAAY;gCACZ,UAAU;;;;;;;;;;;;oBAKb,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAK,WAAU;;4CAAkC;4CACnC,KAAK,KAAK,CAAC,WAAW,UAAU,GAAG;4CAAK;;;;;;;;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;0DAAO;;;;;;0DACR,8OAAC;gDAAE,WAAU;0DAAiB,WAAW,KAAK;;;;;;4CAC7C,WAAW,KAAK,KAAK,SAAS,KAAK,kBAClC,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,SAAS,WAAW,KAAK;gDACxD,WAAU;0DACX;;;;;;;;;;;;kDAML,8OAAC;;0DACC,8OAAC;0DAAO;;;;;;0DACR,8OAAC;gDAAE,WAAU;0DAAiB,WAAW,QAAQ;;;;;;4CAChD,WAAW,QAAQ,KAAK,SAAS,QAAQ,kBACxC,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,YAAY,WAAW,QAAQ;gDAC9D,WAAU;0DACX;;;;;;;;;;;;oCAMJ,WAAW,KAAK,kBACf,8OAAC;;0DACC,8OAAC;0DAAO;;;;;;0DACR,8OAAC;gDAAE,WAAU;0DAAiB,WAAW,KAAK;;;;;;4CAC7C,WAAW,KAAK,KAAK,SAAS,KAAK,kBAClC,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,SAAS,WAAW,KAAK;gDACxD,WAAU;0DACX;;;;;;;;;;;;kDAOP,8OAAC;;0DACC,8OAAC;0DAAO;;;;;;0DACR,8OAAC;gDAAE,WAAU;0DAAiB,WAAW,SAAS;;;;;;4CACjD,WAAW,SAAS,KAAK,SAAS,SAAS,kBAC1C,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,aAAa,WAAW,SAAS;gDAChE,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAOP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAO;;;;;;kDACR,8OAAC;wCAAE,WAAU;kDAA8B,WAAW,WAAW;;;;;;oCAChE,WAAW,WAAW,KAAK,SAAS,WAAW,kBAC9C,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB,eAAe,WAAW,WAAW;wCACpE,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAST,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,QAAQ;wCACR,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oDAAsB,OAAO;8DAAW;mDAA5B;;;;;;;;;;;;;;;;;0CAMnB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,QAAQ;wCACR,OAAO,SAAS,SAAS;wCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC9D,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,0BACd,8OAAC;gDAA6B,OAAO,UAAU,KAAK;0DACjD,UAAU,KAAK;+CADL,UAAU,KAAK;;;;;;;;;;;;;;;;0CAQlC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA0C;;;;;;oCAG1D,gCACC,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAI5C,8OAAC;gCACC,MAAK;gCACL,QAAQ;gCACR,KAAI;gCACJ,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gCAC1E,WAAU;gCACV,aAAY;;;;;;4BAIb,iCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;;oDAAiC;oDAClC,KAAK,KAAK,CAAC,gBAAgB,UAAU,GAAG;oDAAK;;;;;;;;;;;;;kDAG9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;oDAAG,gBAAgB,cAAc,CAAC,OAAO,CAAC;;;;;;;0DAC9E,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAqB;oDAAG,gBAAgB,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;oDAAG;oDAAK,gBAAgB,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;0DACzH,8OAAC;gDAAE,WAAU;;kEAAO,8OAAC;kEAAO;;;;;;oDAAmB;oDAAE,gBAAgB,SAAS;;;;;;;4CACzE,gBAAgB,cAAc,KAAK,SAAS,KAAK,kBAChD,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB,SAAS,gBAAgB,cAAc;gDACtE,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,QAAQ;gCACR,MAAM;gCACN,OAAO,SAAS,WAAW;gCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gCAChE,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,QAAQ;gCACR,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC7D,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,UAAU,gBAAgB,SAAS,MAAM,CAAC,MAAM,KAAK;gCACrD,WAAU;0CAET,eAAe,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAMpD", "debugId": null}}]}