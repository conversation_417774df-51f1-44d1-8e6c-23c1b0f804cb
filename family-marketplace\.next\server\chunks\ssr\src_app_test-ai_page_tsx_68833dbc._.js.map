{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/test-ai/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\n\nexport default function TestAI() {\n  const [imageUrl, setImageUrl] = useState('')\n  const [analysis, setAnalysis] = useState<any>(null)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const testImageAnalysis = async () => {\n    if (!imageUrl) {\n      setError('Please enter an image URL')\n      return\n    }\n\n    setLoading(true)\n    setError(null)\n    setAnalysis(null)\n\n    try {\n      const response = await fetch('/api/ai/analyze-image', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ imageUrl })\n      })\n\n      const result = await response.json()\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Analysis failed')\n      }\n\n      setAnalysis(result.analysis)\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Analysis failed')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const testPriceSuggestion = async () => {\n    if (!analysis) {\n      setError('Please analyze an image first')\n      return\n    }\n\n    setLoading(true)\n    setError(null)\n\n    try {\n      const response = await fetch('/api/ai/suggest-price', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          productInfo: {\n            category: analysis.category,\n            subcategory: analysis.subcategory,\n            brand: analysis.brand,\n            model: analysis.model,\n            condition: analysis.condition,\n            description: analysis.description\n          }\n        })\n      })\n\n      const result = await response.json()\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Price suggestion failed')\n      }\n\n      setAnalysis(prev => ({\n        ...prev,\n        priceSuggestion: result.priceSuggestion\n      }))\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Price suggestion failed')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">\n            🤖 AI Integration Test\n          </h1>\n          \n          <div className=\"space-y-6\">\n            {/* Image URL Input */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Test Image URL\n              </label>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"url\"\n                  value={imageUrl}\n                  onChange={(e) => setImageUrl(e.target.value)}\n                  placeholder=\"https://example.com/image.jpg\"\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n                <button\n                  onClick={testImageAnalysis}\n                  disabled={loading || !imageUrl}\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n                >\n                  {loading ? 'Analyzing...' : 'Analyze Image'}\n                </button>\n              </div>\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Try with a product image URL (e.g., from Google Images)\n              </p>\n            </div>\n\n            {/* Sample URLs */}\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Sample Test URLs:</h3>\n              <div className=\"space-y-1\">\n                <button\n                  onClick={() => setImageUrl('https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500')}\n                  className=\"block text-sm text-blue-600 hover:underline\"\n                >\n                  📱 Smartphone\n                </button>\n                <button\n                  onClick={() => setImageUrl('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500')}\n                  className=\"block text-sm text-blue-600 hover:underline\"\n                >\n                  🪑 Chair\n                </button>\n                <button\n                  onClick={() => setImageUrl('https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500')}\n                  className=\"block text-sm text-blue-600 hover:underline\"\n                >\n                  👟 Sneakers\n                </button>\n              </div>\n            </div>\n\n            {/* Error Display */}\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <p className=\"text-red-700\">{error}</p>\n              </div>\n            )}\n\n            {/* Analysis Results */}\n            {analysis && (\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-lg font-semibold text-green-800\">\n                    🎯 Analysis Results\n                  </h3>\n                  <span className=\"text-sm text-green-600\">\n                    Confidence: {Math.round(analysis.confidence * 100)}%\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <strong>Title:</strong>\n                    <p className=\"text-gray-700\">{analysis.title}</p>\n                  </div>\n                  <div>\n                    <strong>Category:</strong>\n                    <p className=\"text-gray-700\">{analysis.category}</p>\n                  </div>\n                  <div>\n                    <strong>Condition:</strong>\n                    <p className=\"text-gray-700\">{analysis.condition}</p>\n                  </div>\n                  {analysis.brand && (\n                    <div>\n                      <strong>Brand:</strong>\n                      <p className=\"text-gray-700\">{analysis.brand}</p>\n                    </div>\n                  )}\n                  {analysis.model && (\n                    <div>\n                      <strong>Model:</strong>\n                      <p className=\"text-gray-700\">{analysis.model}</p>\n                    </div>\n                  )}\n                  {analysis.subcategory && (\n                    <div>\n                      <strong>Subcategory:</strong>\n                      <p className=\"text-gray-700\">{analysis.subcategory}</p>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"mb-4\">\n                  <strong>Description:</strong>\n                  <p className=\"text-gray-700 mt-1\">{analysis.description}</p>\n                </div>\n\n                {analysis.tags && analysis.tags.length > 0 && (\n                  <div className=\"mb-4\">\n                    <strong>Tags:</strong>\n                    <div className=\"flex flex-wrap gap-2 mt-1\">\n                      {analysis.tags.map((tag: string, index: number) => (\n                        <span\n                          key={index}\n                          className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded\"\n                        >\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Price Suggestion */}\n                {analysis.priceSuggestion ? (\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                    <h4 className=\"font-semibold text-yellow-800 mb-2\">💰 Price Suggestion</h4>\n                    <div className=\"text-sm\">\n                      <p><strong>Suggested Price:</strong> R{analysis.priceSuggestion.suggestedPrice}</p>\n                      <p><strong>Price Range:</strong> R{analysis.priceSuggestion.priceRange.min} - R{analysis.priceSuggestion.priceRange.max}</p>\n                      <p><strong>Confidence:</strong> {Math.round(analysis.priceSuggestion.confidence * 100)}%</p>\n                      <p className=\"mt-2\"><strong>Reasoning:</strong> {analysis.priceSuggestion.reasoning}</p>\n                    </div>\n                  </div>\n                ) : (\n                  <button\n                    onClick={testPriceSuggestion}\n                    disabled={loading}\n                    className=\"bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 disabled:opacity-50\"\n                  >\n                    {loading ? 'Getting Price...' : 'Get Price Suggestion'}\n                  </button>\n                )}\n              </div>\n            )}\n\n            {/* Image Preview */}\n            {imageUrl && (\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Image Preview:</h3>\n                <img\n                  src={imageUrl}\n                  alt=\"Test image\"\n                  className=\"max-w-full h-auto max-h-64 rounded-lg\"\n                  onError={() => setError('Failed to load image. Please check the URL.')}\n                />\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU;YACb,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,YAAY;QAEZ,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,YAAY,OAAO,QAAQ;QAC7B,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU;YACb,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;wBACX,UAAU,SAAS,QAAQ;wBAC3B,aAAa,SAAS,WAAW;wBACjC,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;wBACrB,WAAW,SAAS,SAAS;wBAC7B,aAAa,SAAS,WAAW;oBACnC;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,iBAAiB,OAAO,eAAe;gBACzC,CAAC;QACH,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDACC,SAAS;gDACT,UAAU,WAAW,CAAC;gDACtB,WAAU;0DAET,UAAU,iBAAiB;;;;;;;;;;;;kDAGhC,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAOJ,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;4BAKhC,0BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAGrD,8OAAC;gDAAK,WAAU;;oDAAyB;oDAC1B,KAAK,KAAK,CAAC,SAAS,UAAU,GAAG;oDAAK;;;;;;;;;;;;;kDAIvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,KAAK;;;;;;;;;;;;0DAE9C,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,QAAQ;;;;;;;;;;;;0DAEjD,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,SAAS;;;;;;;;;;;;4CAEjD,SAAS,KAAK,kBACb,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,KAAK;;;;;;;;;;;;4CAG/C,SAAS,KAAK,kBACb,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,KAAK;;;;;;;;;;;;4CAG/C,SAAS,WAAW,kBACnB,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;kEACR,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,WAAW;;;;;;;;;;;;;;;;;;kDAKxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;0DACR,8OAAC;gDAAE,WAAU;0DAAsB,SAAS,WAAW;;;;;;;;;;;;oCAGxD,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,mBACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;0DACR,8OAAC;gDAAI,WAAU;0DACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,KAAa,sBAC/B,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;oCAWd,SAAS,eAAe,iBACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAyB;4DAAG,SAAS,eAAe,CAAC,cAAc;;;;;;;kEAC9E,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAqB;4DAAG,SAAS,eAAe,CAAC,UAAU,CAAC,GAAG;4DAAC;4DAAK,SAAS,eAAe,CAAC,UAAU,CAAC,GAAG;;;;;;;kEACvH,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAoB;4DAAE,KAAK,KAAK,CAAC,SAAS,eAAe,CAAC,UAAU,GAAG;4DAAK;;;;;;;kEACvF,8OAAC;wDAAE,WAAU;;0EAAO,8OAAC;0EAAO;;;;;;4DAAmB;4DAAE,SAAS,eAAe,CAAC,SAAS;;;;;;;;;;;;;;;;;;6DAIvF,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,UAAU,qBAAqB;;;;;;;;;;;;4BAOvC,0BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;wCACV,SAAS,IAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C", "debugId": null}}]}