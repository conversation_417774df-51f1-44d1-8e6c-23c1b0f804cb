'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import Image from 'next/image'
import { UploadResult } from '@/types'

// Define interfaces locally to avoid import issues
interface AIAnalysisResult {
  category: string
  subcategory?: string
  title: string
  description: string
  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'
  brand?: string
  model?: string
  tags: string[]
  confidence: number
}

interface ImageUploadProps {
  onUpload: (result: UploadResult) => void
  onAnalysis?: (analysis: AIAnalysisResult) => void
  maxFiles?: number
  className?: string
}

export default function ImageUpload({ 
  onUpload, 
  onAnalysis, 
  maxFiles = 5, 
  className = '' 
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<UploadResult[]>([])
  const [error, setError] = useState<string | null>(null)

  const uploadImage = async (file: File): Promise<UploadResult> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    })

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.error || 'Upload failed')
    }

    return result.data
  }

  const analyzeImage = async (imageUrl: string): Promise<AIAnalysisResult> => {
    const response = await fetch('/api/ai/analyze-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ imageUrl })
    })

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.error || 'Analysis failed')
    }

    return result.analysis
  }

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (uploadedImages.length + acceptedFiles.length > maxFiles) {
      setError(`Maximum ${maxFiles} images allowed`)
      return
    }

    setUploading(true)
    setError(null)

    try {
      const uploadPromises = acceptedFiles.map(uploadImage)
      const uploadResults = await Promise.all(uploadPromises)
      
      setUploadedImages(prev => [...prev, ...uploadResults])
      
      // Notify parent component
      uploadResults.forEach(result => onUpload(result))

      // Analyze the first uploaded image if analysis callback is provided
      if (onAnalysis && uploadResults.length > 0) {
        setAnalyzing(true)
        try {
          const analysis = await analyzeImage(uploadResults[0].url)
          onAnalysis(analysis)
        } catch (analysisError) {
          console.error('Analysis failed:', analysisError)
          // Don't show error for analysis failure - it's optional
        } finally {
          setAnalyzing(false)
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }, [uploadedImages.length, maxFiles, onUpload, onAnalysis])

  const removeImage = async (index: number) => {
    const image = uploadedImages[index]
    
    try {
      // Delete from server
      await fetch(`/api/upload?filename=${image.filename}`, {
        method: 'DELETE'
      })
      
      // Remove from state
      setUploadedImages(prev => prev.filter((_, i) => i !== index))
    } catch (error) {
      console.error('Failed to delete image:', error)
    }
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.gif', '.bmp', '.tiff', '.tif', '.svg']
    },
    maxFiles: maxFiles - uploadedImages.length,
    disabled: uploading || uploadedImages.length >= maxFiles
  })

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
          ${uploading ? 'opacity-50 cursor-not-allowed' : ''}
          ${uploadedImages.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        {uploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="text-gray-600">Uploading images...</p>
          </div>
        ) : analyzing ? (
          <div className="space-y-2">
            <div className="animate-pulse">
              <div className="h-8 w-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mx-auto"></div>
            </div>
            <p className="text-gray-600">🤖 AI is analyzing your image...</p>
            <p className="text-sm text-gray-500">This will help suggest category, title, and description</p>
          </div>
        ) : uploadedImages.length >= maxFiles ? (
          <p className="text-gray-500">Maximum {maxFiles} images uploaded</p>
        ) : (
          <div className="space-y-2">
            <div className="text-4xl">📸</div>
            <p className="text-lg font-medium">
              {isDragActive ? 'Drop images here' : 'Upload product images'}
            </p>
            <p className="text-gray-500">
              Drag & drop or click to select ({uploadedImages.length}/{maxFiles})
            </p>
            <p className="text-sm text-blue-600">
              ✨ AI will automatically analyze your images to suggest details
            </p>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Uploaded Images */}
      {uploadedImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {uploadedImages.map((image, index) => (
            <div key={image.filename} className="relative group">
              <div className="aspect-square relative rounded-lg overflow-hidden border border-gray-200">
                <Image
                  src={image.url}
                  alt={`Upload ${index + 1}`}
                  fill
                  className="object-cover"
                />
                <button
                  onClick={() => removeImage(index)}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-1 truncate">
                {(image.size / 1024).toFixed(1)}KB
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
