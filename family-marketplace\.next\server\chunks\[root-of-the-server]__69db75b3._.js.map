{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/test-image-url/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { imageUrl } = body\n\n    console.log('Testing image URL:', imageUrl)\n\n    // Test if we can fetch the image\n    const response = await fetch(imageUrl, {\n      method: 'HEAD',\n      headers: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n      }\n    })\n\n    console.log('Image fetch response:', response.status, response.statusText)\n    console.log('Content-Type:', response.headers.get('content-type'))\n    console.log('Content-Length:', response.headers.get('content-length'))\n\n    return NextResponse.json({\n      success: true,\n      accessible: response.ok,\n      status: response.status,\n      statusText: response.statusText,\n      contentType: response.headers.get('content-type'),\n      contentLength: response.headers.get('content-length'),\n      url: imageUrl\n    })\n  } catch (error) {\n    console.error('Image URL test error:', error)\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Test failed',\n      url: request.body\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,GAAG;QAErB,QAAQ,GAAG,CAAC,sBAAsB;QAElC,iCAAiC;QACjC,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS;gBACP,cAAc;YAChB;QACF;QAEA,QAAQ,GAAG,CAAC,yBAAyB,SAAS,MAAM,EAAE,SAAS,UAAU;QACzE,QAAQ,GAAG,CAAC,iBAAiB,SAAS,OAAO,CAAC,GAAG,CAAC;QAClD,QAAQ,GAAG,CAAC,mBAAmB,SAAS,OAAO,CAAC,GAAG,CAAC;QAEpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,YAAY,SAAS,EAAE;YACvB,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,aAAa,SAAS,OAAO,CAAC,GAAG,CAAC;YAClC,eAAe,SAAS,OAAO,CAAC,GAAG,CAAC;YACpC,KAAK;QACP;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,KAAK,QAAQ,IAAI;QACnB;IACF;AACF", "debugId": null}}]}