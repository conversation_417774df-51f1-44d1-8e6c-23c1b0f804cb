'use client'

import { useState } from 'react'
import { Upload, Camera, Wand2, DollarSign, Save, Eye } from 'lucide-react'

interface ProductData {
  title: string
  description: string
  category: string
  subcategory: string
  condition: string
  brand: string
  model: string
  price: number
  tags: string[]
  images: string[]
}

interface AIAnalysisResult {
  category: string
  subcategory: string
  title: string
  description: string
  condition: string
  brand: string
  model: string
  tags: string[]
  confidence: number
}

interface PriceSuggestion {
  suggestedPrice: number
  priceRange: { min: number; max: number }
  confidence: number
  reasoning: string
}

export default function CreateListingPage() {
  const [productData, setProductData] = useState<ProductData>({
    title: '',
    description: '',
    category: '',
    subcategory: '',
    condition: 'GOOD',
    brand: '',
    model: '',
    price: 0,
    tags: [],
    images: []
  })

  const [uploadedImages, setUploadedImages] = useState<string[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isPriceSuggesting, setIsPriceSuggesting] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysisResult | null>(null)
  const [priceSuggestion, setPriceSuggestion] = useState<PriceSuggestion | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const categories = [
    'Electronics', 'Furniture', 'Clothing', 'Books', 'Sports & Outdoors',
    'Home & Garden', 'Toys & Games', 'Automotive', 'Health & Beauty', 'Other'
  ]

  const conditions = [
    { value: 'NEW', label: 'New' },
    { value: 'LIKE_NEW', label: 'Like New' },
    { value: 'GOOD', label: 'Good' },
    { value: 'FAIR', label: 'Fair' },
    { value: 'POOR', label: 'Poor' }
  ]

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files) return

    const formData = new FormData()
    Array.from(files).forEach(file => {
      formData.append('files', file)
    })

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        const newImages = data.urls || [data.url]
        setUploadedImages(prev => [...prev, ...newImages])
        setProductData(prev => ({
          ...prev,
          images: [...prev.images, ...newImages]
        }))
      }
    } catch (error) {
      console.error('Upload failed:', error)
    }
  }

  const analyzeWithAI = async () => {
    if (uploadedImages.length === 0) {
      setErrors({ images: 'Please upload at least one image first' })
      return
    }

    setIsAnalyzing(true)
    setErrors({})

    try {
      const response = await fetch('/api/ai/analyze-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageUrl: uploadedImages[0] })
      })

      if (response.ok) {
        const analysis = await response.json()
        setAiAnalysis(analysis)
        
        // Auto-fill form with AI analysis
        setProductData(prev => ({
          ...prev,
          title: analysis.title || prev.title,
          description: analysis.description || prev.description,
          category: analysis.category || prev.category,
          subcategory: analysis.subcategory || prev.subcategory,
          condition: analysis.condition || prev.condition,
          brand: analysis.brand || prev.brand,
          model: analysis.model || prev.model,
          tags: analysis.tags || prev.tags
        }))
      } else {
        const error = await response.json()
        setErrors({ ai: error.error || 'Failed to analyze image' })
      }
    } catch (error) {
      setErrors({ ai: 'Failed to analyze image. Please try again.' })
    } finally {
      setIsAnalyzing(false)
    }
  }

  const getSuggestedPrice = async () => {
    if (!productData.category || !productData.condition) {
      setErrors({ price: 'Please fill in category and condition first' })
      return
    }

    setIsPriceSuggesting(true)
    setErrors({})

    try {
      const response = await fetch('/api/ai/suggest-price', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          category: productData.category,
          subcategory: productData.subcategory,
          brand: productData.brand,
          model: productData.model,
          condition: productData.condition,
          description: productData.description
        })
      })

      if (response.ok) {
        const suggestion = await response.json()
        setPriceSuggestion(suggestion)
        setProductData(prev => ({
          ...prev,
          price: suggestion.suggestedPrice
        }))
      } else {
        const error = await response.json()
        setErrors({ price: error.error || 'Failed to get price suggestion' })
      }
    } catch (error) {
      setErrors({ price: 'Failed to get price suggestion. Please try again.' })
    } finally {
      setIsPriceSuggesting(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    const newErrors: Record<string, string> = {}
    if (!productData.title) newErrors.title = 'Title is required'
    if (!productData.description) newErrors.description = 'Description is required'
    if (!productData.category) newErrors.category = 'Category is required'
    if (!productData.price || productData.price <= 0) newErrors.price = 'Valid price is required'
    if (uploadedImages.length === 0) newErrors.images = 'At least one image is required'

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    setIsSubmitting(true)
    setErrors({})

    try {
      // Format data for API
      const listingData = {
        title: productData.title,
        description: productData.description,
        price: productData.price,
        condition: productData.condition,
        category: productData.category,
        subcategory: productData.subcategory,
        brand: productData.brand,
        model: productData.model,
        location: 'South Africa',
        images: productData.images.map((url, index) => ({
          url,
          altText: `${productData.title} - Image ${index + 1}`,
          isPrimary: index === 0,
          order: index
        })),
        aiGeneratedTitle: aiAnalysis?.title,
        aiGeneratedDescription: aiAnalysis?.description,
        aiSuggestedPrice: priceSuggestion?.suggestedPrice,
        aiConfidenceScore: aiAnalysis?.confidence,
        aiTags: productData.tags
      }

      const response = await fetch('/api/listings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(listingData)
      })

      if (response.ok) {
        const result = await response.json()
        // Redirect to listing page or show success message
        alert('Listing created successfully!')
        window.location.href = `/listings/${result.listing.id}`
      } else {
        const error = await response.json()
        setErrors({ submit: error.error || 'Failed to create listing' })
      }
    } catch (error) {
      setErrors({ submit: 'Failed to create listing. Please try again.' })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-6">
            <Camera className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Create New Listing</h1>
            <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded">
              AI-Powered
            </span>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Image Upload Section */}
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">
                Product Images *
              </label>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Click to upload images or drag and drop</p>
                  <p className="text-sm text-gray-500 mt-2">PNG, JPG, WEBP up to 10MB each</p>
                </label>
              </div>

              {uploadedImages.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {uploadedImages.map((url, index) => (
                    <div key={index} className="relative">
                      <img
                        src={url}
                        alt={`Product ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg border"
                      />
                    </div>
                  ))}
                </div>
              )}

              {errors.images && (
                <p className="text-red-600 text-sm">{errors.images}</p>
              )}

              {/* AI Analysis Button */}
              {uploadedImages.length > 0 && (
                <button
                  type="button"
                  onClick={analyzeWithAI}
                  disabled={isAnalyzing}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  <Wand2 className="w-5 h-5" />
                  {isAnalyzing ? 'Analyzing with AI...' : 'Analyze with AI'}
                </button>
              )}

              {errors.ai && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-600 text-sm">{errors.ai}</p>
                </div>
              )}

              {aiAnalysis && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Wand2 className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-800">AI Analysis Complete</span>
                    <span className="text-sm text-green-600">
                      Confidence: {Math.round(aiAnalysis.confidence * 100)}%
                    </span>
                  </div>
                  <p className="text-green-700 text-sm">
                    Form has been auto-filled with AI suggestions. Review and edit as needed.
                  </p>
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Title *
                </label>
                <input
                  type="text"
                  value={productData.title}
                  onChange={(e) => setProductData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter product title"
                />
                {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  value={productData.category}
                  onChange={(e) => setProductData(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select category</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {errors.category && <p className="text-red-600 text-sm mt-1">{errors.category}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Condition *
                </label>
                <select
                  value={productData.condition}
                  onChange={(e) => setProductData(prev => ({ ...prev, condition: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {conditions.map(condition => (
                    <option key={condition.value} value={condition.value}>
                      {condition.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Brand
                </label>
                <input
                  type="text"
                  value={productData.brand}
                  onChange={(e) => setProductData(prev => ({ ...prev, brand: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Brand name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Model
                </label>
                <input
                  type="text"
                  value={productData.model}
                  onChange={(e) => setProductData(prev => ({ ...prev, model: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Model name/number"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={productData.description}
                onChange={(e) => setProductData(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe your product in detail..."
              />
              {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
            </div>

            {/* Price Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700">
                  Price (ZAR) *
                </label>
                <button
                  type="button"
                  onClick={getSuggestedPrice}
                  disabled={isPriceSuggesting || !productData.category}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
                >
                  <DollarSign className="w-4 h-4" />
                  {isPriceSuggesting ? 'Getting Suggestion...' : 'Get AI Price Suggestion'}
                </button>
              </div>

              <input
                type="number"
                value={productData.price || ''}
                onChange={(e) => setProductData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
              {errors.price && <p className="text-red-600 text-sm mt-1">{errors.price}</p>}

              {priceSuggestion && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <DollarSign className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-800">AI Price Suggestion</span>
                  </div>
                  <div className="text-sm text-blue-700 space-y-1">
                    <p><strong>Suggested:</strong> R{priceSuggestion.suggestedPrice.toLocaleString()}</p>
                    <p><strong>Range:</strong> R{priceSuggestion.priceRange.min.toLocaleString()} - R{priceSuggestion.priceRange.max.toLocaleString()}</p>
                    <p><strong>Reasoning:</strong> {priceSuggestion.reasoning}</p>
                    <p><strong>Confidence:</strong> {Math.round(priceSuggestion.confidence * 100)}%</p>
                  </div>
                </div>
              )}
            </div>

            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600 text-sm">{errors.submit}</p>
              </div>
            )}

            {/* Submit Buttons */}
            <div className="flex gap-4 pt-6">
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 flex items-center justify-center gap-2"
              >
                <Save className="w-5 h-5" />
                {isSubmitting ? 'Creating Listing...' : 'Create Listing'}
              </button>
              
              <button
                type="button"
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 flex items-center gap-2"
              >
                <Eye className="w-5 h-5" />
                Preview
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
