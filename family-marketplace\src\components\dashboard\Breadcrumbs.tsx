import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[]
  userType: 'PRIVATE' | 'VENDOR' | 'ADMIN'
}

export default function Breadcrumbs({ items, userType }: BreadcrumbsProps) {
  const getHomeHref = () => {
    switch (userType) {
      case 'ADMIN': return '/admin'
      case 'VENDOR': return '/dashboard'
      case 'PRIVATE': return '/dashboard/private'
      default: return '/dashboard'
    }
  }

  const getHomeName = () => {
    switch (userType) {
      case 'ADMIN': return 'Admin'
      case 'VENDOR': return 'Vendor Dashboard'
      case 'PRIVATE': return 'My Dashboard'
      default: return 'Dashboard'
    }
  }

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
      <Link 
        href={getHomeHref()}
        className="flex items-center hover:text-blue-600 transition-colors"
      >
        <Home className="w-4 h-4 mr-1" />
        {getHomeName()}
      </Link>
      
      {items.map((item, index) => (
        <div key={index} className="flex items-center space-x-2">
          <ChevronRight className="w-4 h-4 text-gray-400" />
          {item.current || !item.href ? (
            <span className="font-medium text-gray-900">{item.label}</span>
          ) : (
            <Link 
              href={item.href}
              className="hover:text-blue-600 transition-colors"
            >
              {item.label}
            </Link>
          )}
        </div>
      ))}
    </nav>
  )
}

// Helper function to generate breadcrumbs based on pathname
export function generateBreadcrumbs(pathname: string, userType: 'PRIVATE' | 'VENDOR' | 'ADMIN'): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []

  // Remove the first segment if it's a dashboard type
  if (segments[0] === 'admin' || segments[0] === 'dashboard') {
    segments.shift()
  }

  // Handle special cases for dashboard/private
  if (segments[0] === 'private') {
    segments.shift()
  }

  // Convert segments to breadcrumbs
  let currentPath = userType === 'ADMIN' ? '/admin' : '/dashboard'
  if (userType === 'PRIVATE') {
    currentPath = '/dashboard/private'
  }

  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const isLast = index === segments.length - 1
    
    // Convert segment to readable label
    const label = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')

    breadcrumbs.push({
      label,
      href: isLast ? undefined : currentPath,
      current: isLast
    })
  })

  return breadcrumbs
}
