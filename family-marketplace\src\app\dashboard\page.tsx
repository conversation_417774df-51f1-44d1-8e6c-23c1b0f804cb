'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Eye,
  Users,
  Package,
  Calendar,
  Star,
  MessageCircle,
  Plus,
  Filter,
  Download
} from 'lucide-react'
import Link from 'next/link'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { QuickStats } from '@/components/dashboard/StatsCard'
import ChartCard, { PlaceholderChart, ChartActions } from '@/components/dashboard/ChartCard'
import ActivityFeed from '@/components/dashboard/ActivityFeed'

interface DashboardStats {
  totalListings: number
  activeListings: number
  totalViews: number
  totalMessages: number
  totalSales: number
  totalRevenue: number
  averageRating: number
  monthlyGrowth: number
  recentActivity: Array<{
    id: string
    type: 'listing' | 'message' | 'sale' | 'review'
    title: string
    description?: string
    timestamp: string
    metadata?: {
      price?: number
      status?: string
      rating?: number
    }
    href?: string
  }>
}

export default function VendorDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/login?message=Please+sign+in+to+access+dashboard&redirect=%2Fdashboard')
      return
    }

    if (session.user.userType !== 'VENDOR') {
      router.push('/?message=Dashboard+access+requires+vendor+account')
      return
    }

    fetchDashboardData()
  }, [session, status, router, timeRange])

  const fetchDashboardData = async () => {
    try {
      const response = await fetch(`/api/dashboard/stats?timeRange=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout userType="VENDOR">
        <div className="p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!session || session.user.userType !== 'VENDOR') {
    return null
  }

  const quickStats = [
    {
      title: 'Total Revenue',
      value: `$${stats?.totalRevenue?.toLocaleString() || '0'}`,
      icon: <DollarSign className="w-6 h-6" />,
      color: 'green' as const,
      change: stats?.totalRevenue ? {
        value: stats.monthlyGrowth,
        type: stats.monthlyGrowth >= 0 ? 'increase' as const : 'decrease' as const,
        period: 'this month'
      } : undefined
    },
    {
      title: 'Active Listings',
      value: stats?.activeListings || 0,
      icon: <Package className="w-6 h-6" />,
      color: 'blue' as const,
      subtitle: `${stats?.totalListings || 0} total`
    },
    {
      title: 'Total Views',
      value: stats?.totalViews || 0,
      icon: <Eye className="w-6 h-6" />,
      color: 'purple' as const,
      change: {
        value: 15,
        type: 'increase' as const,
        period: 'this week'
      }
    },
    {
      title: 'Average Rating',
      value: stats?.averageRating ? `${stats.averageRating.toFixed(1)}/5` : 'N/A',
      icon: <Star className="w-6 h-6" />,
      color: 'orange' as const,
      subtitle: 'From customer reviews'
    }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(amount)
  }

  return (
    <DashboardLayout userType="VENDOR">
      <div>
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {session.user.vendorProfile?.businessName || session.user.firstName}! 🏪
            </h1>
            <p className="text-gray-600 mt-2">
              Here's how your business is performing
            </p>
          </div>

          <div className="flex items-center space-x-4 mt-4 lg:mt-0">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>

            <Link
              href="/create"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all flex items-center gap-2 shadow-lg"
            >
              <Plus className="w-4 h-4" />
              New Listing
            </Link>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mb-8">
          <QuickStats stats={quickStats} loading={loading} />
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Revenue Chart */}
          <ChartCard
            title="Revenue Overview"
            subtitle="Monthly revenue trends"
            actions={<ChartActions />}
          >
            <PlaceholderChart
              type="bar"
              title="Revenue Chart"
              description="Track your monthly revenue performance"
            />
          </ChartCard>

          {/* Recent Activity */}
          <ActivityFeed
            title="Recent Activity"
            items={stats?.recentActivity || []}
            loading={loading}
            emptyMessage="No recent activity to show"
          />
        </div>



          {/* Top Performing Listings */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Top Performing Listings</h3>
            <div className="space-y-4">
              {[1, 2, 3].map((item) => (
                <div key={item} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                    <Package className="w-6 h-6 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">Sample Product {item}</h4>
                    <p className="text-sm text-gray-600">125 views • 5 inquiries</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">R 1,250</p>
                    <p className="text-sm text-green-600">+15%</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Messages */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Recent Messages</h3>
              <Link href="/messages" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                View all
              </Link>
            </div>
            <div className="space-y-4">
              {[1, 2, 3].map((item) => (
                <div key={item} className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    U{item}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">User {item}</p>
                    <p className="text-sm text-gray-600">Interested in your listing...</p>
                    <p className="text-xs text-gray-500">2 hours ago</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            <div className="space-y-3">
              <Link
                href="/create"
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Create New Listing
              </Link>
              <Link
                href="/listings"
                className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center gap-2"
              >
                <Package className="w-4 h-4" />
                Manage Listings
              </Link>
              <Link
                href="/messages"
                className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center gap-2"
              >
                <MessageCircle className="w-4 h-4" />
                View Messages
              </Link>
              <Link
                href="/profile"
                className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                Edit Profile
              </Link>
            </div>
          </div>

          {/* Account Status */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Account Status</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Account Type</span>
                <span className="text-sm font-medium text-purple-600">Business Vendor</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Verification</span>
                <span className="text-sm font-medium text-green-600">Verified ✓</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Subscription</span>
                <span className="text-sm font-medium text-blue-600">Active</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Next Billing</span>
                <span className="text-sm text-gray-900">Jan 15, 2025</span>
              </div>
              
              <div className="pt-4 border-t border-gray-200">
                <Link
                  href="/pricing"
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  Upgrade Plan →
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* AI Insights */}
        <div className="mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <div className="bg-purple-100 p-2 rounded-lg mr-3">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">🤖 AI Insights</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Optimal Posting Time</h4>
              <p className="text-sm text-gray-600">
                Your listings get 40% more views when posted on weekends between 2-6 PM
              </p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Price Optimization</h4>
              <p className="text-sm text-gray-600">
                Consider reducing prices by 5-10% on items older than 30 days for faster sales
              </p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Photo Quality</h4>
              <p className="text-sm text-gray-600">
                Listings with 3+ high-quality photos receive 60% more inquiries
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
