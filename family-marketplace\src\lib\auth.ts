import { NextAuthOptions } from 'next-auth'
import Credential<PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from './prisma'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          },
          include: {
            vendorProfile: true
          }
        })

        if (!user) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          userType: user.userType,
          isVerified: user.isVerified,
          avatar: user.avatar,
          vendorProfile: user.vendorProfile
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.username = user.username
        token.userType = user.userType
        token.isVerified = user.isVerified
        token.vendorProfile = user.vendorProfile
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.username = token.username as string
        session.user.userType = token.userType as string
        session.user.isVerified = token.isVerified as boolean
        session.user.vendorProfile = token.vendorProfile as any
      }
      return session
    }
  },
  pages: {
    signIn: '/login',
    signUp: '/register'
  }
}

// Helper functions for user management
export async function createUser(userData: {
  email: string
  username: string
  password: string
  firstName?: string
  lastName?: string
  userType: 'PRIVATE' | 'VENDOR'
  vendorData?: {
    businessName: string
    description?: string
    website?: string
  }
}) {
  const hashedPassword = await bcrypt.hash(userData.password, 12)

  const user = await prisma.user.create({
    data: {
      email: userData.email,
      username: userData.username,
      password: hashedPassword,
      firstName: userData.firstName,
      lastName: userData.lastName,
      userType: userData.userType,
      vendorProfile: userData.userType === 'VENDOR' && userData.vendorData ? {
        create: {
          businessName: userData.vendorData.businessName,
          description: userData.vendorData.description,
          website: userData.vendorData.website
        }
      } : undefined
    },
    include: {
      vendorProfile: true
    }
  })

  return user
}

export async function getUserByEmail(email: string) {
  return await prisma.user.findUnique({
    where: { email },
    include: {
      vendorProfile: true
    }
  })
}

export async function getUserByUsername(username: string) {
  return await prisma.user.findUnique({
    where: { username },
    include: {
      vendorProfile: true
    }
  })
}
