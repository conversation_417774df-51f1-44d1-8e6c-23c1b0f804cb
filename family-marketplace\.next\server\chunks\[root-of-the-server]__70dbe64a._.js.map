{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/upload.ts"], "sourcesContent": ["import { writeFile, mkdir } from 'fs/promises'\nimport { existsSync } from 'fs'\nimport path from 'path'\nimport sharp from 'sharp'\n\nexport interface UploadResult {\n  url: string\n  filename: string\n  size: number\n  width: number\n  height: number\n}\n\nexport class ImageUploadService {\n  private static readonly UPLOAD_DIR = './public/uploads'\n  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n  private static readonly ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp']\n  private static readonly MAX_WIDTH = 1920\n  private static readonly MAX_HEIGHT = 1920\n  private static readonly QUALITY = 85\n\n  /**\n   * Initialize upload directory\n   */\n  static async init(): Promise<void> {\n    if (!existsSync(this.UPLOAD_DIR)) {\n      await mkdir(this.UPLOAD_DIR, { recursive: true })\n    }\n  }\n\n  /**\n   * Upload and process image file\n   */\n  static async uploadImage(file: File): Promise<UploadResult> {\n    await this.init()\n\n    // Validate file\n    this.validateFile(file)\n\n    // Generate unique filename\n    const filename = this.generateFilename(file.name)\n    const filepath = path.join(this.UPLOAD_DIR, filename)\n\n    try {\n      // Convert File to Buffer\n      const buffer = Buffer.from(await file.arrayBuffer())\n\n      // Process image with Sharp\n      const processedImage = await this.processImage(buffer)\n\n      // Save processed image\n      await writeFile(filepath, processedImage.data)\n\n      // Get the base URL for the full image URL\n      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'\n\n      return {\n        url: `${baseUrl}/uploads/${filename}`,\n        filename,\n        size: processedImage.data.length,\n        width: processedImage.width,\n        height: processedImage.height\n      }\n    } catch (error) {\n      console.error('Error uploading image:', error)\n      throw new Error('Failed to upload image')\n    }\n  }\n\n  /**\n   * Process image: resize, optimize, and convert to WebP\n   */\n  private static async processImage(buffer: Buffer): Promise<{\n    data: Buffer\n    width: number\n    height: number\n  }> {\n    const image = sharp(buffer)\n    const metadata = await image.metadata()\n\n    // Calculate new dimensions while maintaining aspect ratio\n    let { width = 0, height = 0 } = metadata\n    \n    if (width > this.MAX_WIDTH || height > this.MAX_HEIGHT) {\n      const ratio = Math.min(this.MAX_WIDTH / width, this.MAX_HEIGHT / height)\n      width = Math.round(width * ratio)\n      height = Math.round(height * ratio)\n    }\n\n    // Process image\n    const processedBuffer = await image\n      .resize(width, height, {\n        fit: 'inside',\n        withoutEnlargement: true\n      })\n      .webp({ quality: this.QUALITY })\n      .toBuffer()\n\n    return {\n      data: processedBuffer,\n      width: width || 0,\n      height: height || 0\n    }\n  }\n\n  /**\n   * Validate uploaded file\n   */\n  private static validateFile(file: File): void {\n    // Check file size\n    if (file.size > this.MAX_FILE_SIZE) {\n      throw new Error(`File size too large. Maximum size is ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`)\n    }\n\n    // Check file type\n    if (!this.ALLOWED_TYPES.includes(file.type)) {\n      throw new Error(`Invalid file type. Allowed types: ${this.ALLOWED_TYPES.join(', ')}`)\n    }\n  }\n\n  /**\n   * Generate unique filename\n   */\n  private static generateFilename(originalName: string): string {\n    const timestamp = Date.now()\n    const random = Math.random().toString(36).substring(2, 15)\n    const extension = '.webp' // Always convert to WebP\n    \n    return `${timestamp}-${random}${extension}`\n  }\n\n  /**\n   * Delete uploaded image\n   */\n  static async deleteImage(filename: string): Promise<void> {\n    try {\n      const filepath = path.join(this.UPLOAD_DIR, filename)\n      const fs = await import('fs/promises')\n      await fs.unlink(filepath)\n    } catch (error) {\n      console.error('Error deleting image:', error)\n      // Don't throw - file might already be deleted\n    }\n  }\n}\n\n/**\n * Utility function to convert File to base64 for AI analysis\n */\nexport async function fileToBase64(file: File): Promise<string> {\n  const buffer = Buffer.from(await file.arrayBuffer())\n  return `data:${file.type};base64,${buffer.toString('base64')}`\n}\n\n/**\n * Utility function to get image dimensions\n */\nexport async function getImageDimensions(file: File): Promise<{ width: number; height: number }> {\n  const buffer = Buffer.from(await file.arrayBuffer())\n  const metadata = await sharp(buffer).metadata()\n  \n  return {\n    width: metadata.width || 0,\n    height: metadata.height || 0\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;AAUO,MAAM;IACX,OAAwB,aAAa,mBAAkB;IACvD,OAAwB,gBAAgB,KAAK,OAAO,KAAK,OAAO;KAAR;IACxD,OAAwB,gBAAgB;QAAC;QAAc;QAAa;KAAa,CAAA;IACjF,OAAwB,YAAY,KAAI;IACxC,OAAwB,aAAa,KAAI;IACzC,OAAwB,UAAU,GAAE;IAEpC;;GAEC,GACD,aAAa,OAAsB;QACjC,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,UAAU,GAAG;YAChC,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAK;QACjD;IACF;IAEA;;GAEC,GACD,aAAa,YAAY,IAAU,EAAyB;QAC1D,MAAM,IAAI,CAAC,IAAI;QAEf,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;QAElB,2BAA2B;QAC3B,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAChD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAE5C,IAAI;YACF,yBAAyB;YACzB,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;YAEjD,2BAA2B;YAC3B,MAAM,iBAAiB,MAAM,IAAI,CAAC,YAAY,CAAC;YAE/C,uBAAuB;YACvB,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU,eAAe,IAAI;YAE7C,0CAA0C;YAC1C,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI;YAE5C,OAAO;gBACL,KAAK,GAAG,QAAQ,SAAS,EAAE,UAAU;gBACrC;gBACA,MAAM,eAAe,IAAI,CAAC,MAAM;gBAChC,OAAO,eAAe,KAAK;gBAC3B,QAAQ,eAAe,MAAM;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAqB,aAAa,MAAc,EAI7C;QACD,MAAM,QAAQ,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE;QACpB,MAAM,WAAW,MAAM,MAAM,QAAQ;QAErC,0DAA0D;QAC1D,IAAI,EAAE,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG;QAEhC,IAAI,QAAQ,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,UAAU,EAAE;YACtD,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,UAAU,GAAG;YACjE,QAAQ,KAAK,KAAK,CAAC,QAAQ;YAC3B,SAAS,KAAK,KAAK,CAAC,SAAS;QAC/B;QAEA,gBAAgB;QAChB,MAAM,kBAAkB,MAAM,MAC3B,MAAM,CAAC,OAAO,QAAQ;YACrB,KAAK;YACL,oBAAoB;QACtB,GACC,IAAI,CAAC;YAAE,SAAS,IAAI,CAAC,OAAO;QAAC,GAC7B,QAAQ;QAEX,OAAO;YACL,MAAM;YACN,OAAO,SAAS;YAChB,QAAQ,UAAU;QACpB;IACF;IAEA;;GAEC,GACD,OAAe,aAAa,IAAU,EAAQ;QAC5C,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE;YAClC,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QAChG;QAEA,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YAC3C,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO;QACtF;IACF;IAEA;;GAEC,GACD,OAAe,iBAAiB,YAAoB,EAAU;QAC5D,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QACvD,MAAM,YAAY,QAAQ,yBAAyB;;QAEnD,OAAO,GAAG,UAAU,CAAC,EAAE,SAAS,WAAW;IAC7C;IAEA;;GAEC,GACD,aAAa,YAAY,QAAgB,EAAiB;QACxD,IAAI;YACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC5C,MAAM,KAAK;YACX,MAAM,GAAG,MAAM,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,8CAA8C;QAChD;IACF;AACF;AAKO,eAAe,aAAa,IAAU;IAC3C,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;IACjD,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,OAAO,QAAQ,CAAC,WAAW;AAChE;AAKO,eAAe,mBAAmB,IAAU;IACjD,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;IACjD,MAAM,WAAW,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QAAQ,QAAQ;IAE7C,OAAO;QACL,OAAO,SAAS,KAAK,IAAI;QACzB,QAAQ,SAAS,MAAM,IAAI;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { ImageUploadService } from '@/lib/upload'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file provided' },\n        { status: 400 }\n      )\n    }\n\n    const result = await ImageUploadService.uploadImage(file)\n\n    return NextResponse.json({\n      success: true,\n      data: result\n    })\n  } catch (error) {\n    console.error('Upload error:', error)\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Upload failed',\n        success: false \n      },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const filename = searchParams.get('filename')\n\n    if (!filename) {\n      return NextResponse.json(\n        { error: 'No filename provided' },\n        { status: 400 }\n      )\n    }\n\n    await ImageUploadService.deleteImage(filename)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Image deleted successfully'\n    })\n  } catch (error) {\n    console.error('Delete error:', error)\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Delete failed',\n        success: false \n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC;QAEpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,sHAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}