{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/south-african-categories.ts"], "sourcesContent": ["// South African localized categories and terminology for the marketplace\n\nexport interface CategoryWithSubcategories {\n  name: string\n  icon: string\n  subcategories: string[]\n}\n\nexport const SOUTH_AFRICAN_CATEGORIES: CategoryWithSubcategories[] = [\n  {\n    name: 'Bakkies & Cars',\n    icon: '🚗',\n    subcategories: [\n      '<PERSON><PERSON><PERSON><PERSON> (Pickup Trucks)',\n      'Sedans',\n      'Hatchbacks', \n      'SUVs',\n      'Combis (Minivans)',\n      'Motorcycles',\n      'Scooters',\n      'Car Parts & Accessories',\n      'Tyres & Wheels',\n      'Car Audio & Electronics'\n    ]\n  },\n  {\n    name: 'Electronics',\n    icon: '📱',\n    subcategories: [\n      'Cell Phones',\n      'Laptops & Computers',\n      'TVs & Audio',\n      'Gaming Consoles',\n      'Cameras',\n      'Tablets',\n      'Smart Watches',\n      'Headphones',\n      'Chargers & Cables',\n      'Other Electronics'\n    ]\n  },\n  {\n    name: 'Furniture',\n    icon: '🪑',\n    subcategories: [\n      'Lounge Suites',\n      'Bedroom Sets',\n      'Dining Room Sets',\n      'Office Furniture',\n      'Outdoor Furniture',\n      'Wardrobes',\n      'Beds & Mattresses',\n      'Tables & Chairs',\n      'Storage Solutions',\n      'Antiques'\n    ]\n  },\n  {\n    name: 'Home & Garden',\n    icon: '🏠',\n    subcategories: [\n      'Garden Tools',\n      'Braai Equipment',\n      'Pool Equipment',\n      'Security Systems',\n      'Solar Equipment',\n      'Kitchen Appliances',\n      'Cleaning Equipment',\n      'Home Decor',\n      'Lighting',\n      'Plumbing & Electrical'\n    ]\n  },\n  {\n    name: 'Sport & Outdoor',\n    icon: '⚽',\n    subcategories: [\n      'Rugby Equipment',\n      'Cricket Equipment',\n      'Soccer Equipment',\n      'Golf Equipment',\n      'Fishing Gear',\n      'Camping & Hiking',\n      'Bicycles',\n      'Gym Equipment',\n      'Water Sports',\n      'Hunting Equipment'\n    ]\n  },\n  {\n    name: 'Clothing',\n    icon: '👕',\n    subcategories: [\n      'Mens Clothing',\n      'Womens Clothing',\n      'Kids Clothing',\n      'Shoes',\n      'Accessories',\n      'Traditional Wear',\n      'Work Wear',\n      'Sports Wear',\n      'Formal Wear',\n      'Vintage Clothing'\n    ]\n  },\n  {\n    name: 'Health & Beauty',\n    icon: '💄',\n    subcategories: [\n      'Skincare',\n      'Makeup',\n      'Hair Care',\n      'Perfumes',\n      'Health Supplements',\n      'Medical Equipment',\n      'Fitness Equipment',\n      'Beauty Tools',\n      'Natural Products',\n      'Baby Care'\n    ]\n  },\n  {\n    name: 'Books',\n    icon: '📚',\n    subcategories: [\n      'Textbooks',\n      'Fiction',\n      'Non-Fiction',\n      'Childrens Books',\n      'Academic Books',\n      'Afrikaans Books',\n      'Local Authors',\n      'Religious Books',\n      'Magazines',\n      'Comics'\n    ]\n  },\n  {\n    name: 'Toys & Games',\n    icon: '🧸',\n    subcategories: [\n      'Baby Toys',\n      'Educational Toys',\n      'Action Figures',\n      'Board Games',\n      'Video Games',\n      'Outdoor Toys',\n      'Arts & Crafts',\n      'Building Blocks',\n      'Dolls',\n      'Remote Control Toys'\n    ]\n  },\n  {\n    name: 'Other',\n    icon: '📦',\n    subcategories: [\n      'Musical Instruments',\n      'Art & Collectibles',\n      'Business Equipment',\n      'Industrial Equipment',\n      'Agricultural Equipment',\n      'Construction Tools',\n      'Pet Supplies',\n      'Travel Gear',\n      'Hobbies & Crafts',\n      'Miscellaneous'\n    ]\n  }\n]\n\n// Extract just the category names for dropdowns\nexport const CATEGORY_NAMES = SOUTH_AFRICAN_CATEGORIES.map(cat => cat.name)\n\n// Get subcategories for a specific category\nexport const getSubcategories = (categoryName: string): string[] => {\n  const category = SOUTH_AFRICAN_CATEGORIES.find(cat => cat.name === categoryName)\n  return category?.subcategories || []\n}\n\n// South African specific terms mapping\nexport const SA_TERMINOLOGY = {\n  'pickup truck': 'bakkie',\n  'pickup trucks': 'bakkies',\n  'minivan': 'combi',\n  'minivans': 'combis',\n  'traffic light': 'robot',\n  'traffic lights': 'robots',\n  'barbecue': 'braai',\n  'barbecues': 'braais',\n  'soda': 'cool drink',\n  'apartment': 'flat',\n  'apartments': 'flats',\n  'elevator': 'lift',\n  'elevators': 'lifts',\n  'flashlight': 'torch',\n  'flashlights': 'torches',\n  'cell phone': 'cellphone',\n  'mobile phone': 'cellphone',\n  'gas station': 'petrol station',\n  'trunk': 'boot',\n  'hood': 'bonnet',\n  'faucet': 'tap',\n  'faucets': 'taps'\n}\n\n// Convert text to use South African terminology\nexport const localizeSAText = (text: string): string => {\n  let localizedText = text\n  \n  Object.entries(SA_TERMINOLOGY).forEach(([american, southAfrican]) => {\n    const regex = new RegExp(`\\\\b${american}\\\\b`, 'gi')\n    localizedText = localizedText.replace(regex, southAfrican)\n  })\n  \n  return localizedText\n}\n\n// Common South African brands for automotive\nexport const SA_AUTOMOTIVE_BRANDS = [\n  'Toyota', 'Ford', 'Volkswagen', 'BMW', 'Mercedes-Benz', 'Audi', 'Nissan',\n  'Hyundai', 'Kia', 'Mazda', 'Honda', 'Chevrolet', 'Isuzu', 'Mitsubishi',\n  'Subaru', 'Volvo', 'Land Rover', 'Jaguar', 'Peugeot', 'Renault',\n  'Mahindra', 'Tata', 'GWM', 'Haval', 'Chery', 'JAC', 'Foton'\n]\n\n// Popular bakkie models in South Africa\nexport const SA_BAKKIE_MODELS = [\n  'Toyota Hilux', 'Ford Ranger', 'Isuzu D-Max', 'Volkswagen Amarok',\n  'Nissan Navara', 'Mitsubishi Triton', 'Mazda BT-50', 'GWM P-Series',\n  'Mahindra Pik Up', 'JAC T6', 'Foton Tunland'\n]\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;;;;;;;AAQlE,MAAM,2BAAwD;IACnE;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,iBAAiB,yBAAyB,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;AAGnE,MAAM,mBAAmB,CAAC;IAC/B,MAAM,WAAW,yBAAyB,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;IACnE,OAAO,UAAU,iBAAiB,EAAE;AACtC;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,aAAa;IACb,cAAc;IACd,YAAY;IACZ,aAAa;IACb,cAAc;IACd,eAAe;IACf,cAAc;IACd,gBAAgB;IAChB,eAAe;IACf,SAAS;IACT,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,gBAAgB;IAEpB,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,UAAU,aAAa;QAC9D,MAAM,QAAQ,IAAI,OAAO,CAAC,GAAG,EAAE,SAAS,GAAG,CAAC,EAAE;QAC9C,gBAAgB,cAAc,OAAO,CAAC,OAAO;IAC/C;IAEA,OAAO;AACT;AAGO,MAAM,uBAAuB;IAClC;IAAU;IAAQ;IAAc;IAAO;IAAiB;IAAQ;IAChE;IAAW;IAAO;IAAS;IAAS;IAAa;IAAS;IAC1D;IAAU;IAAS;IAAc;IAAU;IAAW;IACtD;IAAY;IAAQ;IAAO;IAAS;IAAS;IAAO;CACrD;AAGM,MAAM,mBAAmB;IAC9B;IAAgB;IAAe;IAAe;IAC9C;IAAiB;IAAqB;IAAe;IACrD;IAAmB;IAAU;CAC9B", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/browse/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Search, Filter, Grid, List, MapPin, Heart, Eye, Clock } from 'lucide-react'\nimport { CATEGORY_NAMES } from '@/lib/south-african-categories'\nimport Link from 'next/link'\n\ninterface Listing {\n  id: string\n  title: string\n  description: string\n  price: number\n  condition: string\n  category: string\n  subcategory?: string\n  brand?: string\n  model?: string\n  location: string\n  views: number\n  createdAt: string\n  images: Array<{\n    id: string\n    url: string\n    isPrimary: boolean\n  }>\n  user: {\n    id: string\n    username: string\n    firstName: string\n    lastName: string\n    userType: string\n    vendorProfile?: {\n      businessName: string\n      verified: boolean\n    }\n  }\n}\n\nexport default function BrowsePage() {\n  const [listings, setListings] = useState<Listing[]>([])\n  const [loading, setLoading] = useState(true)\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [selectedCondition, setSelectedCondition] = useState('')\n  const [priceRange, setPriceRange] = useState({ min: '', max: '' })\n  const [userType, setUserType] = useState('')\n  const [sortBy, setSortBy] = useState('newest')\n  const [showFilters, setShowFilters] = useState(false)\n\n  const conditions = ['NEW', 'LIKE_NEW', 'GOOD', 'FAIR', 'POOR']\n  const sortOptions = [\n    { value: 'newest', label: 'Newest First' },\n    { value: 'oldest', label: 'Oldest First' },\n    { value: 'price_low', label: 'Price: Low to High' },\n    { value: 'price_high', label: 'Price: High to Low' },\n    { value: 'popular', label: 'Most Popular' }\n  ]\n\n  useEffect(() => {\n    fetchListings()\n  }, [selectedCategory, selectedCondition, priceRange, userType, sortBy, searchQuery])\n\n  const fetchListings = async () => {\n    setLoading(true)\n    try {\n      const params = new URLSearchParams()\n      if (searchQuery) params.append('search', searchQuery)\n      if (selectedCategory) params.append('category', selectedCategory)\n      if (selectedCondition) params.append('condition', selectedCondition)\n      if (priceRange.min) params.append('minPrice', priceRange.min)\n      if (priceRange.max) params.append('maxPrice', priceRange.max)\n      if (userType) params.append('userType', userType)\n      \n      const response = await fetch(`/api/listings?${params.toString()}`)\n      if (response.ok) {\n        const data = await response.json()\n        setListings(data.listings || [])\n      }\n    } catch (error) {\n      console.error('Failed to fetch listings:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-ZA', {\n      style: 'currency',\n      currency: 'ZAR',\n      minimumFractionDigits: 0\n    }).format(price)\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-ZA', {\n      day: 'numeric',\n      month: 'short',\n      year: 'numeric'\n    })\n  }\n\n  const clearFilters = () => {\n    setSearchQuery('')\n    setSelectedCategory('')\n    setSelectedCondition('')\n    setPriceRange({ min: '', max: '' })\n    setUserType('')\n    setSortBy('newest')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Browse Marketplace</h1>\n              <p className=\"text-gray-600 mt-1\">Discover amazing items from our community</p>\n            </div>\n            \n            {/* Search Bar */}\n            <div className=\"flex-1 max-w-lg\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search for items...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"flex flex-col lg:flex-row gap-6\">\n          {/* Filters Sidebar */}\n          <div className={`lg:w-64 ${showFilters ? 'block' : 'hidden lg:block'}`}>\n            <div className=\"bg-white rounded-lg shadow-sm p-6 sticky top-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Filters</h3>\n                <button\n                  onClick={clearFilters}\n                  className=\"text-sm text-blue-600 hover:text-blue-700\"\n                >\n                  Clear All\n                </button>\n              </div>\n\n              <div className=\"space-y-6\">\n                {/* Category Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Category\n                  </label>\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">All Categories</option>\n                    {CATEGORY_NAMES.map(category => (\n                      <option key={category} value={category}>{category}</option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Condition Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Condition\n                  </label>\n                  <select\n                    value={selectedCondition}\n                    onChange={(e) => setSelectedCondition(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">Any Condition</option>\n                    {conditions.map(condition => (\n                      <option key={condition} value={condition}>\n                        {condition.replace('_', ' ')}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Price Range */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Price Range (ZAR)\n                  </label>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <input\n                      type=\"number\"\n                      placeholder=\"Min\"\n                      value={priceRange.min}\n                      onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                    <input\n                      type=\"number\"\n                      placeholder=\"Max\"\n                      value={priceRange.max}\n                      onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n\n                {/* Seller Type */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Seller Type\n                  </label>\n                  <select\n                    value={userType}\n                    onChange={(e) => setUserType(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">All Sellers</option>\n                    <option value=\"PRIVATE\">Private Sellers</option>\n                    <option value=\"VENDOR\">Business Vendors</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {/* Controls Bar */}\n            <div className=\"bg-white rounded-lg shadow-sm p-4 mb-6\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n                <div className=\"flex items-center gap-4\">\n                  <button\n                    onClick={() => setShowFilters(!showFilters)}\n                    className=\"lg:hidden flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\n                  >\n                    <Filter className=\"w-4 h-4\" />\n                    Filters\n                  </button>\n                  \n                  <span className=\"text-sm text-gray-600\">\n                    {loading ? 'Loading...' : `${listings.length} items found`}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center gap-4\">\n                  {/* Sort */}\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {sortOptions.map(option => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n\n                  {/* View Mode */}\n                  <div className=\"flex border border-gray-300 rounded-lg\">\n                    <button\n                      onClick={() => setViewMode('grid')}\n                      className={`p-2 ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-50'}`}\n                    >\n                      <Grid className=\"w-4 h-4\" />\n                    </button>\n                    <button\n                      onClick={() => setViewMode('list')}\n                      className={`p-2 ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-50'}`}\n                    >\n                      <List className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Listings Grid/List */}\n            {loading ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {[...Array(6)].map((_, i) => (\n                  <div key={i} className=\"bg-white rounded-lg shadow-sm p-4 animate-pulse\">\n                    <div className=\"bg-gray-200 h-48 rounded-lg mb-4\"></div>\n                    <div className=\"space-y-2\">\n                      <div className=\"bg-gray-200 h-4 rounded w-3/4\"></div>\n                      <div className=\"bg-gray-200 h-4 rounded w-1/2\"></div>\n                      <div className=\"bg-gray-200 h-4 rounded w-1/4\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : listings.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 text-6xl mb-4\">🔍</div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No items found</h3>\n                <p className=\"text-gray-600\">Try adjusting your search criteria or filters</p>\n              </div>\n            ) : viewMode === 'grid' ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {listings.map((listing) => (\n                  <Link\n                    key={listing.id}\n                    href={`/listings/${listing.id}`}\n                    className=\"bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden\"\n                  >\n                    <div className=\"relative\">\n                      <img\n                        src={listing.images.find(img => img.isPrimary)?.url || listing.images[0]?.url || '/placeholder-image.jpg'}\n                        alt={listing.title}\n                        className=\"w-full h-48 object-cover\"\n                      />\n                      <div className=\"absolute top-2 right-2\">\n                        <button className=\"p-2 bg-white/80 rounded-full hover:bg-white transition-colors\">\n                          <Heart className=\"w-4 h-4 text-gray-600\" />\n                        </button>\n                      </div>\n                      <div className=\"absolute bottom-2 left-2\">\n                        <span className=\"bg-black/70 text-white text-xs px-2 py-1 rounded\">\n                          {listing.condition.replace('_', ' ')}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"p-4\">\n                      <h3 className=\"font-semibold text-gray-900 mb-1 line-clamp-2\">\n                        {listing.title}\n                      </h3>\n                      <p className=\"text-2xl font-bold text-blue-600 mb-2\">\n                        {formatPrice(listing.price)}\n                      </p>\n                      <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        {listing.location}\n                      </div>\n                      <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                        <div className=\"flex items-center\">\n                          <Eye className=\"w-4 h-4 mr-1\" />\n                          {listing.views} views\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Clock className=\"w-4 h-4 mr-1\" />\n                          {formatDate(listing.createdAt)}\n                        </div>\n                      </div>\n                      <div className=\"mt-2 flex items-center\">\n                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                          listing.user.userType === 'VENDOR' \n                            ? 'bg-purple-100 text-purple-800' \n                            : 'bg-blue-100 text-blue-800'\n                        }`}>\n                          {listing.user.userType === 'VENDOR' ? '🏪' : '👤'} \n                          {listing.user.userType === 'VENDOR' \n                            ? listing.user.vendorProfile?.businessName || 'Business'\n                            : `${listing.user.firstName} ${listing.user.lastName}`\n                          }\n                        </span>\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {listings.map((listing) => (\n                  <Link\n                    key={listing.id}\n                    href={`/listings/${listing.id}`}\n                    className=\"bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 flex gap-4\"\n                  >\n                    <img\n                      src={listing.images.find(img => img.isPrimary)?.url || listing.images[0]?.url || '/placeholder-image.jpg'}\n                      alt={listing.title}\n                      className=\"w-32 h-32 object-cover rounded-lg flex-shrink-0\"\n                    />\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-gray-900 mb-1\">\n                        {listing.title}\n                      </h3>\n                      <p className=\"text-gray-600 text-sm mb-2 line-clamp-2\">\n                        {listing.description}\n                      </p>\n                      <p className=\"text-2xl font-bold text-blue-600 mb-2\">\n                        {formatPrice(listing.price)}\n                      </p>\n                      <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                        <div className=\"flex items-center\">\n                          <MapPin className=\"w-4 h-4 mr-1\" />\n                          {listing.location}\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Eye className=\"w-4 h-4 mr-1\" />\n                          {listing.views} views\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Clock className=\"w-4 h-4 mr-1\" />\n                          {formatDate(listing.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAsCe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAI,KAAK;IAAG;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,aAAa;QAAC;QAAO;QAAY;QAAQ;QAAQ;KAAO;IAC9D,MAAM,cAAc;QAClB;YAAE,OAAO;YAAU,OAAO;QAAe;QACzC;YAAE,OAAO;YAAU,OAAO;QAAe;QACzC;YAAE,OAAO;YAAa,OAAO;QAAqB;QAClD;YAAE,OAAO;YAAc,OAAO;QAAqB;QACnD;YAAE,OAAO;YAAW,OAAO;QAAe;KAC3C;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAkB;QAAmB;QAAY;QAAU;QAAQ;KAAY;IAEnF,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,aAAa,OAAO,MAAM,CAAC,UAAU;YACzC,IAAI,kBAAkB,OAAO,MAAM,CAAC,YAAY;YAChD,IAAI,mBAAmB,OAAO,MAAM,CAAC,aAAa;YAClD,IAAI,WAAW,GAAG,EAAE,OAAO,MAAM,CAAC,YAAY,WAAW,GAAG;YAC5D,IAAI,WAAW,GAAG,EAAE,OAAO,MAAM,CAAC,YAAY,WAAW,GAAG;YAC5D,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;YAExC,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;YACjE,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,oBAAoB;QACpB,qBAAqB;QACrB,cAAc;YAAE,KAAK;YAAI,KAAK;QAAG;QACjC,YAAY;QACZ,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAIpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,QAAQ,EAAE,cAAc,UAAU,mBAAmB;sCACpE,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAKH,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,4IAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAA,yBAClB,8OAAC;oEAAsB,OAAO;8EAAW;mEAA5B;;;;;;;;;;;;;;;;;0DAMnB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACpD,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,WAAW,GAAG,CAAC,CAAA,0BACd,8OAAC;oEAAuB,OAAO;8EAC5B,UAAU,OAAO,CAAC,KAAK;mEADb;;;;;;;;;;;;;;;;;0DAQnB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,aAAY;gEACZ,OAAO,WAAW,GAAG;gEACrB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACxE,WAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,aAAY;gEACZ,OAAO,WAAW,GAAG;gEACrB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACxE,WAAU;;;;;;;;;;;;;;;;;;0DAMhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQjC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,eAAe,CAAC;wDAC/B,WAAU;;0EAEV,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAIhC,8OAAC;wDAAK,WAAU;kEACb,UAAU,eAAe,GAAG,SAAS,MAAM,CAAC,YAAY,CAAC;;;;;;;;;;;;0DAI9D,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wDACzC,WAAU;kEAET,YAAY,GAAG,CAAC,CAAA,uBACf,8OAAC;gEAA0B,OAAO,OAAO,KAAK;0EAC3C,OAAO,KAAK;+DADF,OAAO,KAAK;;;;;;;;;;kEAO7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,YAAY;gEAC3B,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,2BAA2B,kCAAkC;0EAErG,cAAA,8OAAC,yMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,SAAS,IAAM,YAAY;gEAC3B,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,2BAA2B,kCAAkC;0EAErG,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQzB,wBACC,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;2CALT;;;;;;;;;2CAUZ,SAAS,MAAM,KAAK,kBACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;2CAE7B,aAAa,uBACf,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;4CAC/B,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAK,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,SAAS,GAAG,OAAO,QAAQ,MAAM,CAAC,EAAE,EAAE,OAAO;4DACjF,KAAK,QAAQ,KAAK;4DAClB,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAO,WAAU;0EAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAGrB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,QAAQ,SAAS,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;8DAKtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,YAAY,QAAQ,KAAK;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,QAAQ,QAAQ;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEACd,QAAQ,KAAK;wEAAC;;;;;;;8EAEjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,WAAW,QAAQ,SAAS;;;;;;;;;;;;;sEAGjC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAW,CAAC,oEAAoE,EACpF,QAAQ,IAAI,CAAC,QAAQ,KAAK,WACtB,kCACA,6BACJ;;oEACC,QAAQ,IAAI,CAAC,QAAQ,KAAK,WAAW,OAAO;oEAC5C,QAAQ,IAAI,CAAC,QAAQ,KAAK,WACvB,QAAQ,IAAI,CAAC,aAAa,EAAE,gBAAgB,aAC5C,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;2CApDzD,QAAQ,EAAE;;;;;;;;;yDA6DrB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;4CAC/B,WAAU;;8DAEV,8OAAC;oDACC,KAAK,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,SAAS,GAAG,OAAO,QAAQ,MAAM,CAAC,EAAE,EAAE,OAAO;oDACjF,KAAK,QAAQ,KAAK;oDAClB,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;sEAEtB,8OAAC;4DAAE,WAAU;sEACV,YAAY,QAAQ,KAAK;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,QAAQ,QAAQ;;;;;;;8EAEnB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEACd,QAAQ,KAAK;wEAAC;;;;;;;8EAEjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;2CA9B9B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CnC", "debugId": null}}]}