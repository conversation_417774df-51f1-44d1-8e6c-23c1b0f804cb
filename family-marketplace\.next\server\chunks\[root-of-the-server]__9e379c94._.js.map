{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          },\n          include: {\n            vendorProfile: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          userType: user.userType,\n          isVerified: user.isVerified,\n          avatar: user.avatar,\n          vendorProfile: user.vendorProfile\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n        token.username = user.username\n        token.userType = user.userType\n        token.isVerified = user.isVerified\n        token.vendorProfile = user.vendorProfile\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n        session.user.username = token.username as string\n        session.user.userType = token.userType as string\n        session.user.isVerified = token.isVerified as boolean\n        session.user.vendorProfile = token.vendorProfile as any\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/login',\n    signUp: '/register'\n  }\n}\n\n// Helper functions for user management\nexport async function createUser(userData: {\n  email: string\n  username: string\n  password: string\n  firstName?: string\n  lastName?: string\n  userType: 'PRIVATE' | 'VENDOR'\n  vendorData?: {\n    businessName: string\n    description?: string\n    website?: string\n  }\n}) {\n  const hashedPassword = await bcrypt.hash(userData.password, 12)\n\n  const user = await prisma.user.create({\n    data: {\n      email: userData.email,\n      username: userData.username,\n      password: hashedPassword,\n      firstName: userData.firstName,\n      lastName: userData.lastName,\n      userType: userData.userType,\n      vendorProfile: userData.userType === 'VENDOR' && userData.vendorData ? {\n        create: {\n          businessName: userData.vendorData.businessName,\n          description: userData.vendorData.description,\n          website: userData.vendorData.website\n        }\n      } : undefined\n    },\n    include: {\n      vendorProfile: true\n    }\n  })\n\n  return user\n}\n\nexport async function getUserByEmail(email: string) {\n  return await prisma.user.findUnique({\n    where: { email },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n\nexport async function getUserByUsername(username: string) {\n  return await prisma.user.findUnique({\n    where: { username },\n    include: {\n      vendorProfile: true\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;oBACA,SAAS;wBACP,eAAe;oBACjB;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,aAAa;gBACnC;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,aAAa,GAAG,KAAK,aAAa;YAC1C;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;YAClD;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,eAAe,WAAW,QAYhC;IACC,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;IAE5D,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM;YACJ,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;YAC3B,UAAU;YACV,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;YAC3B,eAAe,SAAS,QAAQ,KAAK,YAAY,SAAS,UAAU,GAAG;gBACrE,QAAQ;oBACN,cAAc,SAAS,UAAU,CAAC,YAAY;oBAC9C,aAAa,SAAS,UAAU,CAAC,WAAW;oBAC5C,SAAS,SAAS,UAAU,CAAC,OAAO;gBACtC;YACF,IAAI;QACN;QACA,SAAS;YACP,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,KAAa;IAChD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAM;QACf,SAAS;YACP,eAAe;QACjB;IACF;AACF;AAEO,eAAe,kBAAkB,QAAgB;IACtD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,eAAe;QACjB;IACF;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai'\n\nif (!process.env.OPENAI_API_KEY) {\n  throw new Error('OPENAI_API_KEY is not set in environment variables')\n}\n\nexport const openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n})\n\n// AI Service Types\nexport interface ImageAnalysisResult {\n  category: string\n  subcategory?: string\n  title: string\n  description: string\n  condition: 'NEW' | 'LIKE_NEW' | 'GOOD' | 'FAIR' | 'POOR'\n  brand?: string\n  model?: string\n  tags: string[]\n  confidence: number\n}\n\nexport interface PriceSuggestion {\n  suggestedPrice: number\n  priceRange: {\n    min: number\n    max: number\n  }\n  confidence: number\n  reasoning: string\n}\n\n// Core AI Services\nexport class AIService {\n  /**\n   * Analyze an image to extract product information\n   */\n  static async analyzeImage(imageUrl: string): Promise<ImageAnalysisResult> {\n    // If it's a localhost URL, convert to base64\n    if (imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {\n      return this.analyzeImageFromLocalhost(imageUrl)\n    }\n    try {\n      const response = await openai.chat.completions.create({\n        model: \"gpt-4o\",\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                type: \"text\",\n                text: `Analyze this product image for a South African marketplace listing. Use South African terminology (e.g., \"bakkie\" for pickup truck, \"robot\" for traffic light). Extract information in JSON format:\n                {\n                  \"category\": \"Main category: Electronics, Furniture, Clothing, Books, Sport & Outdoor, Home & Garden, Toys & Games, Bakkies & Cars, Health & Beauty, or Other\",\n                  \"subcategory\": \"Specific subcategory if applicable (use SA terms: bakkie, sedan, hatchback, SUV, etc.)\",\n                  \"title\": \"Descriptive title suitable for South African buyers using local terminology\",\n                  \"description\": \"Detailed description highlighting key features and condition using South African terms\",\n                  \"condition\": \"Realistic condition: NEW, LIKE_NEW, GOOD, FAIR, or POOR\",\n                  \"brand\": \"Brand name if visible/identifiable\",\n                  \"model\": \"Model name/number if visible\",\n                  \"tags\": [\"relevant\", \"search\", \"keywords\", \"for\", \"SA\", \"market\"],\n                  \"confidence\": 0.95\n                }\n\n                Guidelines:\n                - Focus on details visible in the image\n                - Consider South African market preferences\n                - Be realistic about condition assessment\n                - Include relevant search terms for local buyers\n                - If uncertain about any field, use null`\n              },\n              {\n                type: \"image_url\",\n                image_url: {\n                  url: imageUrl\n                }\n              }\n            ]\n          }\n        ],\n        max_tokens: 1000,\n        temperature: 0.3\n      })\n\n      const content = response.choices[0]?.message?.content\n      if (!content) {\n        throw new Error('No response from OpenAI')\n      }\n\n      // Parse JSON response (handle markdown code blocks)\n      const cleanContent = this.extractJsonFromResponse(content)\n      const result = JSON.parse(cleanContent) as ImageAnalysisResult\n      \n      // Log analytics\n      await this.logAnalytics('image_analysis', { imageUrl }, result, result.confidence, 'gpt-4-vision-preview')\n      \n      return result\n    } catch (error) {\n      console.error('Error analyzing image:', error)\n\n      // Provide more specific error messages\n      if (error instanceof Error) {\n        if (error.message.includes('Invalid image')) {\n          throw new Error('The image format is not supported or the image is corrupted')\n        }\n        if (error.message.includes('rate limit') || error.message.includes('429') || error.message.includes('quota')) {\n          throw new Error('🚫 AI service quota exceeded. Please check your OpenAI billing or try again later.')\n        }\n        if (error.message.includes('API key')) {\n          throw new Error('AI service configuration error')\n        }\n      }\n\n      throw new Error('Failed to analyze image. Please try again.')\n    }\n  }\n\n  /**\n   * Analyze image from localhost by converting to base64\n   */\n  static async analyzeImageFromLocalhost(imageUrl: string): Promise<ImageAnalysisResult> {\n    try {\n      // Fetch the image from localhost\n      const response = await fetch(imageUrl)\n      if (!response.ok) {\n        throw new Error('Failed to fetch image from localhost')\n      }\n\n      const buffer = await response.arrayBuffer()\n      const base64 = Buffer.from(buffer).toString('base64')\n      const mimeType = response.headers.get('content-type') || 'image/jpeg'\n      const base64Url = `data:${mimeType};base64,${base64}`\n\n      const openaiResponse = await openai.chat.completions.create({\n        model: \"gpt-4o\",\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                type: \"text\",\n                text: `Analyze this product image for a South African marketplace listing. Use South African terminology (e.g., \"bakkie\" for pickup truck, \"robot\" for traffic light). Extract information in JSON format:\n                {\n                  \"category\": \"Main category: Electronics, Furniture, Clothing, Books, Sport & Outdoor, Home & Garden, Toys & Games, Bakkies & Cars, Health & Beauty, or Other\",\n                  \"subcategory\": \"Specific subcategory if applicable (use SA terms: bakkie, sedan, hatchback, SUV, etc.)\",\n                  \"title\": \"Descriptive title suitable for South African buyers using local terminology\",\n                  \"description\": \"Detailed description highlighting key features and condition using South African terms\",\n                  \"condition\": \"Realistic condition: NEW, LIKE_NEW, GOOD, FAIR, or POOR\",\n                  \"brand\": \"Brand name if visible/identifiable\",\n                  \"model\": \"Model name/number if visible\",\n                  \"tags\": [\"relevant\", \"search\", \"keywords\", \"for\", \"SA\", \"market\"],\n                  \"confidence\": 0.95\n                }\n\n                Guidelines:\n                - Focus on details visible in the image\n                - Consider South African market preferences\n                - Be realistic about condition assessment\n                - Include relevant search terms for local buyers\n                - If uncertain about any field, use null`\n              },\n              {\n                type: \"image_url\",\n                image_url: {\n                  url: base64Url,\n                  detail: \"high\"\n                }\n              }\n            ]\n          }\n        ],\n        max_tokens: 1000,\n        temperature: 0.3\n      })\n\n      const content = openaiResponse.choices[0]?.message?.content\n      if (!content) {\n        throw new Error('No response from OpenAI')\n      }\n\n      // Parse JSON response (handle markdown code blocks)\n      const cleanContent = this.extractJsonFromResponse(content)\n      const result = JSON.parse(cleanContent) as ImageAnalysisResult\n\n      // Log analytics\n      await this.logAnalytics('image_analysis', { imageUrl }, result, result.confidence, 'gpt-4o')\n\n      return result\n    } catch (error) {\n      console.error('Error analyzing localhost image:', error)\n\n      // Provide more specific error messages\n      if (error instanceof Error) {\n        if (error.message.includes('Invalid image')) {\n          throw new Error('The image format is not supported or the image is corrupted')\n        }\n        if (error.message.includes('rate limit') || error.message.includes('429') || error.message.includes('quota')) {\n          throw new Error('🚫 AI service quota exceeded. Please check your OpenAI billing or try again later.')\n        }\n        if (error.message.includes('API key')) {\n          throw new Error('AI service configuration error')\n        }\n      }\n\n      throw new Error('Failed to analyze image. Please try again.')\n    }\n  }\n\n  /**\n   * Generate price suggestions based on product details and market data\n   */\n  static async suggestPrice(productInfo: {\n    category: string\n    subcategory?: string\n    brand?: string\n    model?: string\n    condition: string\n    description: string\n  }): Promise<PriceSuggestion> {\n    try {\n      const response = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are a South African marketplace pricing expert. Suggest realistic prices in South African Rand (ZAR) based on local market conditions, brand reputation, item condition, and economic factors specific to South Africa.\"\n          },\n          {\n            role: \"user\",\n            content: `Suggest a realistic price for this product in the South African market (ZAR):\n\n            Product Details:\n            - Category: ${productInfo.category}\n            - Subcategory: ${productInfo.subcategory || 'N/A'}\n            - Brand: ${productInfo.brand || 'N/A'}\n            - Model: ${productInfo.model || 'N/A'}\n            - Condition: ${productInfo.condition}\n            - Description: ${productInfo.description}\n\n            Consider South African factors:\n            - Local market demand and supply\n            - Brand availability and reputation in SA\n            - Economic conditions and purchasing power\n            - Import costs and local alternatives\n            - Typical depreciation rates for used items\n\n            Return JSON format (prices in ZAR):\n            {\n              \"suggestedPrice\": 1500,\n              \"priceRange\": {\n                \"min\": 1200,\n                \"max\": 1800\n              },\n              \"confidence\": 0.85,\n              \"reasoning\": \"Brief explanation considering SA market factors\"\n            }`\n          }\n        ],\n        max_tokens: 500,\n        temperature: 0.3\n      })\n\n      const content = response.choices[0]?.message?.content\n      if (!content) {\n        throw new Error('No response from OpenAI')\n      }\n\n      const cleanContent = this.extractJsonFromResponse(content)\n      const result = JSON.parse(cleanContent) as PriceSuggestion\n      \n      // Log analytics\n      await this.logAnalytics('price_suggestion', productInfo, result, result.confidence, 'gpt-4')\n      \n      return result\n    } catch (error) {\n      console.error('Error suggesting price:', error)\n      throw new Error('Failed to suggest price')\n    }\n  }\n\n  /**\n   * Generate improved product descriptions\n   */\n  static async enhanceDescription(originalDescription: string, productInfo: any): Promise<string> {\n    try {\n      const response = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are a copywriting expert specializing in marketplace listings. Create compelling, accurate product descriptions that highlight key features and benefits while maintaining honesty about condition and details.\"\n          },\n          {\n            role: \"user\",\n            content: `Enhance this product description to make it more appealing and informative:\n\n            Original: \"${originalDescription}\"\n            \n            Product Info: ${JSON.stringify(productInfo)}\n            \n            Guidelines:\n            - Keep it honest and accurate\n            - Highlight key features and benefits\n            - Use engaging but professional language\n            - Include relevant keywords for searchability\n            - Mention condition appropriately\n            - Keep it concise but informative (2-3 paragraphs max)\n            \n            Return only the enhanced description, no JSON or extra formatting.`\n          }\n        ],\n        max_tokens: 400,\n        temperature: 0.7\n      })\n\n      const enhancedDescription = response.choices[0]?.message?.content?.trim()\n      if (!enhancedDescription) {\n        throw new Error('No response from OpenAI')\n      }\n\n      // Log analytics\n      await this.logAnalytics('description_enhancement', { originalDescription, productInfo }, { enhancedDescription }, 0.9, 'gpt-4')\n      \n      return enhancedDescription\n    } catch (error) {\n      console.error('Error enhancing description:', error)\n      return originalDescription // Fallback to original\n    }\n  }\n\n  /**\n   * Generate auto-responses for common buyer questions\n   */\n  static async generateAutoResponse(question: string, listingInfo: any): Promise<string> {\n    try {\n      const response = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are a helpful assistant for a marketplace seller. Generate polite, informative responses to buyer questions based on the listing information. Be helpful but direct sellers to contact for specific details not in the listing.\"\n          },\n          {\n            role: \"user\",\n            content: `Generate a response to this buyer question:\n\n            Question: \"${question}\"\n            \n            Listing Info: ${JSON.stringify(listingInfo)}\n            \n            Guidelines:\n            - Be polite and helpful\n            - Use information from the listing when available\n            - For specific details not in listing, suggest contacting the seller\n            - Keep responses concise but friendly\n            - Don't make up information not in the listing\n            \n            Return only the response message.`\n          }\n        ],\n        max_tokens: 200,\n        temperature: 0.7\n      })\n\n      const autoResponse = response.choices[0]?.message?.content?.trim()\n      if (!autoResponse) {\n        throw new Error('No response from OpenAI')\n      }\n\n      // Log analytics\n      await this.logAnalytics('auto_response', { question, listingInfo }, { autoResponse }, 0.8, 'gpt-4')\n      \n      return autoResponse\n    } catch (error) {\n      console.error('Error generating auto response:', error)\n      return \"Thank you for your question! Please feel free to contact the seller directly for more specific details about this item.\"\n    }\n  }\n\n  /**\n   * Extract JSON from markdown-wrapped responses\n   */\n  private static extractJsonFromResponse(content: string): string {\n    // Remove markdown code blocks if present\n    const jsonMatch = content.match(/```(?:json)?\\s*(\\{[\\s\\S]*?\\})\\s*```/)\n    if (jsonMatch) {\n      return jsonMatch[1].trim()\n    }\n\n    // If no markdown blocks, try to find JSON object\n    const jsonObjectMatch = content.match(/\\{[\\s\\S]*\\}/)\n    if (jsonObjectMatch) {\n      return jsonObjectMatch[0].trim()\n    }\n\n    // Return original content if no JSON found\n    return content.trim()\n  }\n\n  /**\n   * Log AI analytics for tracking and optimization\n   */\n  private static async logAnalytics(\n    type: string,\n    input: any,\n    output: any,\n    confidence: number,\n    model: string\n  ): Promise<void> {\n    try {\n      const { prisma } = await import('./prisma')\n      \n      await prisma.aIAnalytics.create({\n        data: {\n          type,\n          input,\n          output,\n          confidence,\n          model,\n          cost: this.estimateCost(type, model) // Rough cost estimation\n        }\n      })\n    } catch (error) {\n      console.error('Error logging analytics:', error)\n      // Don't throw - analytics logging shouldn't break the main functionality\n    }\n  }\n\n  /**\n   * Estimate API costs for budgeting\n   */\n  private static estimateCost(type: string, model: string): number {\n    // Rough cost estimates based on OpenAI pricing (as of 2024)\n    const costs = {\n      'gpt-4': 0.03, // per 1K tokens\n      'gpt-4-vision-preview': 0.01, // per image + text tokens\n      'gpt-3.5-turbo': 0.002 // per 1K tokens\n    }\n    \n    const baseCost = costs[model as keyof typeof costs] || 0.01\n    \n    // Multiply by estimated token usage\n    const tokenMultipliers = {\n      'image_analysis': 800,\n      'price_suggestion': 400,\n      'description_enhancement': 300,\n      'auto_response': 150\n    }\n    \n    const tokens = tokenMultipliers[type as keyof typeof tokenMultipliers] || 200\n    return (baseCost * tokens) / 1000\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC/B,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AA0BO,MAAM;IACX;;GAEC,GACD,aAAa,aAAa,QAAgB,EAAgC;QACxE,6CAA6C;QAC7C,IAAI,SAAS,QAAQ,CAAC,gBAAgB,SAAS,QAAQ,CAAC,cAAc;YACpE,OAAO,IAAI,CAAC,yBAAyB,CAAC;QACxC;QACA,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,CAAC;;;;;;;;;;;;;;;;;;wDAkBiC,CAAC;4BAC3C;4BACA;gCACE,MAAM;gCACN,WAAW;oCACT,KAAK;gCACP;4BACF;yBACD;oBACH;iBACD;gBACD,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,oDAAoD;YACpD,MAAM,eAAe,IAAI,CAAC,uBAAuB,CAAC;YAClD,MAAM,SAAS,KAAK,KAAK,CAAC;YAE1B,gBAAgB;YAChB,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB;gBAAE;YAAS,GAAG,QAAQ,OAAO,UAAU,EAAE;YAEnF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,uCAAuC;YACvC,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAC3C,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,iBAAiB,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;oBAC5G,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACrC,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,0BAA0B,QAAgB,EAAgC;QACrF,IAAI;YACF,iCAAiC;YACjC,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,WAAW;YACzC,MAAM,SAAS,OAAO,IAAI,CAAC,QAAQ,QAAQ,CAAC;YAC5C,MAAM,WAAW,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;YACzD,MAAM,YAAY,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,QAAQ;YAErD,MAAM,iBAAiB,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,CAAC;;;;;;;;;;;;;;;;;;wDAkBiC,CAAC;4BAC3C;4BACA;gCACE,MAAM;gCACN,WAAW;oCACT,KAAK;oCACL,QAAQ;gCACV;4BACF;yBACD;oBACH;iBACD;gBACD,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,UAAU,eAAe,OAAO,CAAC,EAAE,EAAE,SAAS;YACpD,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,oDAAoD;YACpD,MAAM,eAAe,IAAI,CAAC,uBAAuB,CAAC;YAClD,MAAM,SAAS,KAAK,KAAK,CAAC;YAE1B,gBAAgB;YAChB,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB;gBAAE;YAAS,GAAG,QAAQ,OAAO,UAAU,EAAE;YAEnF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAElD,uCAAuC;YACvC,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAC3C,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,iBAAiB,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;oBAC5G,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACrC,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,aAAa,WAOzB,EAA4B;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS,CAAC;;;wBAGE,EAAE,YAAY,QAAQ,CAAC;2BACpB,EAAE,YAAY,WAAW,IAAI,MAAM;qBACzC,EAAE,YAAY,KAAK,IAAI,MAAM;qBAC7B,EAAE,YAAY,KAAK,IAAI,MAAM;yBACzB,EAAE,YAAY,SAAS,CAAC;2BACtB,EAAE,YAAY,WAAW,CAAC;;;;;;;;;;;;;;;;;;aAkBxC,CAAC;oBACJ;iBACD;gBACD,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,eAAe,IAAI,CAAC,uBAAuB,CAAC;YAClD,MAAM,SAAS,KAAK,KAAK,CAAC;YAE1B,gBAAgB;YAChB,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,aAAa,QAAQ,OAAO,UAAU,EAAE;YAEpF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,mBAAmB,mBAA2B,EAAE,WAAgB,EAAmB;QAC9F,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS,CAAC;;uBAEC,EAAE,oBAAoB;;0BAEnB,EAAE,KAAK,SAAS,CAAC,aAAa;;;;;;;;;;8EAUsB,CAAC;oBACrE;iBACD;gBACD,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,sBAAsB,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,SAAS;YACnE,IAAI,CAAC,qBAAqB;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,gBAAgB;YAChB,MAAM,IAAI,CAAC,YAAY,CAAC,2BAA2B;gBAAE;gBAAqB;YAAY,GAAG;gBAAE;YAAoB,GAAG,KAAK;YAEvH,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,oBAAoB,uBAAuB;;QACpD;IACF;IAEA;;GAEC,GACD,aAAa,qBAAqB,QAAgB,EAAE,WAAgB,EAAmB;QACrF,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS,CAAC;;uBAEC,EAAE,SAAS;;0BAER,EAAE,KAAK,SAAS,CAAC,aAAa;;;;;;;;;6CASX,CAAC;oBACpC;iBACD;gBACD,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,SAAS;YAC5D,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,gBAAgB;YAChB,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB;gBAAE;gBAAU;YAAY,GAAG;gBAAE;YAAa,GAAG,KAAK;YAE3F,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAe,wBAAwB,OAAe,EAAU;QAC9D,yCAAyC;QACzC,MAAM,YAAY,QAAQ,KAAK,CAAC;QAChC,IAAI,WAAW;YACb,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI;QAC1B;QAEA,iDAAiD;QACjD,MAAM,kBAAkB,QAAQ,KAAK,CAAC;QACtC,IAAI,iBAAiB;YACnB,OAAO,eAAe,CAAC,EAAE,CAAC,IAAI;QAChC;QAEA,2CAA2C;QAC3C,OAAO,QAAQ,IAAI;IACrB;IAEA;;GAEC,GACD,aAAqB,aACnB,IAAY,EACZ,KAAU,EACV,MAAW,EACX,UAAkB,EAClB,KAAa,EACE;QACf,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,GAAG;YAEnB,MAAM,OAAO,WAAW,CAAC,MAAM,CAAC;gBAC9B,MAAM;oBACJ;oBACA;oBACA;oBACA;oBACA;oBACA,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,OAAO,wBAAwB;gBAC/D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,yEAAyE;QAC3E;IACF;IAEA;;GAEC,GACD,OAAe,aAAa,IAAY,EAAE,KAAa,EAAU;QAC/D,4DAA4D;QAC5D,MAAM,QAAQ;YACZ,SAAS;YACT,wBAAwB;YACxB,iBAAiB,MAAM,gBAAgB;QACzC;QAEA,MAAM,WAAW,KAAK,CAAC,MAA4B,IAAI;QAEvD,oCAAoC;QACpC,MAAM,mBAAmB;YACvB,kBAAkB;YAClB,oBAAoB;YACpB,2BAA2B;YAC3B,iBAAiB;QACnB;QAEA,MAAM,SAAS,gBAAgB,CAAC,KAAsC,IAAI;QAC1E,OAAO,AAAC,WAAW,SAAU;IAC/B;AACF", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/ai/suggest-price/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { AIService } from '@/lib/openai'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      )\n    }\n\n    const body = await request.json()\n    const { productInfo } = body\n\n    if (!productInfo || !productInfo.category || !productInfo.description) {\n      return NextResponse.json(\n        { error: 'Product information (category and description) is required' },\n        { status: 400 }\n      )\n    }\n\n    // Generate price suggestion with AI\n    const priceSuggestion = await AIService.suggestPrice(productInfo)\n\n    return NextResponse.json({\n      success: true,\n      priceSuggestion\n    })\n  } catch (error) {\n    console.error('AI price suggestion error:', error)\n    \n    // Return a more specific error message\n    if (error instanceof Error) {\n      if (error.message.includes('API key')) {\n        return NextResponse.json(\n          { error: 'AI service configuration error' },\n          { status: 500 }\n        )\n      }\n      if (error.message.includes('quota') || error.message.includes('rate limit')) {\n        return NextResponse.json(\n          { error: 'AI service temporarily unavailable. Please try again later.' },\n          { status: 429 }\n        )\n      }\n    }\n\n    return NextResponse.json(\n      { error: 'Failed to generate price suggestion. Please try again.' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,WAAW,EAAE,GAAG;QAExB,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,IAAI,CAAC,YAAY,WAAW,EAAE;YACrE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6D,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,oCAAoC;QACpC,MAAM,kBAAkB,MAAM,sHAAA,CAAA,YAAS,CAAC,YAAY,CAAC;QAErD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAE5C,uCAAuC;QACvC,IAAI,iBAAiB,OAAO;YAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;gBACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiC,GAC1C;oBAAE,QAAQ;gBAAI;YAElB;YACA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,MAAM,OAAO,CAAC,QAAQ,CAAC,eAAe;gBAC3E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA8D,GACvE;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyD,GAClE;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}