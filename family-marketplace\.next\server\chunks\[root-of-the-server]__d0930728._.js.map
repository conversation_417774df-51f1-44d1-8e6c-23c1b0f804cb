{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/api/listings/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const listing = await prisma.listing.findUnique({\n      where: {\n        id: params.id,\n        status: 'ACTIVE'\n      },\n      include: {\n        images: {\n          orderBy: { order: 'asc' }\n        },\n        user: {\n          select: {\n            id: true,\n            username: true,\n            firstName: true,\n            lastName: true,\n            userType: true,\n            isVerified: true,\n            createdAt: true,\n            vendorProfile: {\n              select: {\n                businessName: true,\n                description: true,\n                verified: true\n              }\n            }\n          }\n        }\n      }\n    })\n\n    if (!listing) {\n      return NextResponse.json(\n        { error: 'Listing not found' },\n        { status: 404 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      listing\n    })\n  } catch (error) {\n    console.error('Listing fetch error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const body = await request.json()\n    const {\n      title,\n      description,\n      price,\n      condition,\n      category,\n      subcategory,\n      brand,\n      model,\n      location\n    } = body\n\n    // TODO: Add authentication check\n    // const session = await getServerSession(authOptions)\n    // if (!session?.user?.id) {\n    //   return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    // }\n\n    const listing = await prisma.listing.findUnique({\n      where: { id: params.id }\n    })\n\n    if (!listing) {\n      return NextResponse.json(\n        { error: 'Listing not found' },\n        { status: 404 }\n      )\n    }\n\n    // TODO: Check if user owns the listing\n    // if (listing.userId !== session.user.id) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })\n    // }\n\n    const updatedListing = await prisma.listing.update({\n      where: { id: params.id },\n      data: {\n        title,\n        description,\n        price: parseFloat(price),\n        condition,\n        category,\n        subcategory: subcategory || null,\n        brand: brand || null,\n        model: model || null,\n        location: location || 'South Africa'\n      },\n      include: {\n        images: {\n          orderBy: { order: 'asc' }\n        },\n        user: {\n          select: {\n            id: true,\n            username: true,\n            firstName: true,\n            lastName: true,\n            userType: true,\n            vendorProfile: {\n              select: {\n                businessName: true,\n                verified: true\n              }\n            }\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: 'Listing updated successfully',\n      listing: updatedListing\n    })\n  } catch (error) {\n    console.error('Listing update error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    // TODO: Add authentication check\n    // const session = await getServerSession(authOptions)\n    // if (!session?.user?.id) {\n    //   return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    // }\n\n    const listing = await prisma.listing.findUnique({\n      where: { id: params.id }\n    })\n\n    if (!listing) {\n      return NextResponse.json(\n        { error: 'Listing not found' },\n        { status: 404 }\n      )\n    }\n\n    // TODO: Check if user owns the listing\n    // if (listing.userId !== session.user.id) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })\n    // }\n\n    await prisma.listing.delete({\n      where: { id: params.id }\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: 'Listing deleted successfully'\n    })\n  } catch (error) {\n    console.error('Listing deletion error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ;YACV;YACA,SAAS;gBACP,QAAQ;oBACN,SAAS;wBAAE,OAAO;oBAAM;gBAC1B;gBACA,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,YAAY;wBACZ,WAAW;wBACX,eAAe;4BACb,QAAQ;gCACN,cAAc;gCACd,aAAa;gCACb,UAAU;4BACZ;wBACF;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,WAAW,EACX,KAAK,EACL,SAAS,EACT,QAAQ,EACR,WAAW,EACX,KAAK,EACL,KAAK,EACL,QAAQ,EACT,GAAG;QAEJ,iCAAiC;QACjC,sDAAsD;QACtD,4BAA4B;QAC5B,oFAAoF;QACpF,IAAI;QAEJ,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;QACzB;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,4CAA4C;QAC5C,yEAAyE;QACzE,IAAI;QAEJ,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACjD,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;YACvB,MAAM;gBACJ;gBACA;gBACA,OAAO,WAAW;gBAClB;gBACA;gBACA,aAAa,eAAe;gBAC5B,OAAO,SAAS;gBAChB,OAAO,SAAS;gBAChB,UAAU,YAAY;YACxB;YACA,SAAS;gBACP,QAAQ;oBACN,SAAS;wBAAE,OAAO;oBAAM;gBAC1B;gBACA,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,eAAe;4BACb,QAAQ;gCACN,cAAc;gCACd,UAAU;4BACZ;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,iCAAiC;QACjC,sDAAsD;QACtD,4BAA4B;QAC5B,oFAAoF;QACpF,IAAI;QAEJ,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;QACzB;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,4CAA4C;QAC5C,yEAAyE;QACzE,IAAI;QAEJ,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,OAAO;gBAAE,IAAI,OAAO,EAAE;YAAC;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}