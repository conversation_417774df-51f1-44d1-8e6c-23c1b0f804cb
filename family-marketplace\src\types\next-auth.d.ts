import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      username: string
      firstName?: string
      lastName?: string
      userType: 'PRIVATE' | 'VENDOR' | 'ADMIN'
      isVerified: boolean
      avatar?: string
      vendorProfile?: {
        id: string
        businessName: string
        description?: string
        website?: string
        verified: boolean
      }
    }
  }

  interface User {
    id: string
    email: string
    username: string
    firstName?: string
    lastName?: string
    userType: 'PRIVATE' | 'VENDOR' | 'ADMIN'
    isVerified: boolean
    avatar?: string
    vendorProfile?: {
      id: string
      businessName: string
      description?: string
      website?: string
      verified: boolean
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    username: string
    userType: 'PRIVATE' | 'VENDOR' | 'ADMIN'
    isVerified: boolean
    vendorProfile?: {
      id: string
      businessName: string
      description?: string
      website?: string
      verified: boolean
    }
  }
}
