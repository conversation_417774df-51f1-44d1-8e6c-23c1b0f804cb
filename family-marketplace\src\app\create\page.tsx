'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import CreateListingForm from '@/components/CreateListingForm'
import Link from 'next/link'

export default function CreateListingPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login?message=Please sign in to create a listing&redirect=/create')
    }
  }, [status, router])

  // Show loading state while checking authentication
  if (status === 'loading') {
    return (
      <div className="bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Checking authentication...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show login prompt if not authenticated
  if (status === 'unauthenticated') {
    return (
      <div className="bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="text-6xl mb-6">🔒</div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Sign In Required
            </h1>
            <p className="text-gray-600 mb-8">
              You need to be signed in to create listings. This helps us associate your listings with your account and provide the best AI-powered experience.
            </p>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
                <Link
                  href="/login?redirect=/create"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all"
                >
                  Sign In
                </Link>

                <div className="relative group">
                  <button className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all">
                    Create Account
                  </button>
                  <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                    <div className="py-2">
                      <Link
                        href="/register?type=private&redirect=/create"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50"
                      >
                        <span className="mr-2">👤</span>
                        Private Seller
                      </Link>
                      <Link
                        href="/register?type=vendor&redirect=/create"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-purple-50"
                      >
                        <span className="mr-2">🏪</span>
                        Business Vendor
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-800 mb-2">Why sign in?</h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• AI personalizes suggestions based on your account type</li>
                  <li>• Manage all your listings in one place</li>
                  <li>• Buyers can contact you securely</li>
                  <li>• Track views and engagement on your listings</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // User is authenticated, show the create listing form
  return (
    <div className="bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* User Info Header */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                {session.user.firstName ? session.user.firstName[0] : session.user.username[0].toUpperCase()}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  Creating listing as {session.user.firstName || session.user.username}
                </p>
                <div className="flex items-center">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    session.user.userType === 'VENDOR'
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {session.user.userType === 'VENDOR' ? '🏪 Vendor Account' : '👤 Private Seller'}
                  </span>
                </div>
              </div>
            </div>

            {session.user.userType === 'VENDOR' && session.user.vendorProfile && (
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  {session.user.vendorProfile.businessName}
                </p>
                <p className="text-xs text-gray-500">Business Account</p>
              </div>
            )}
          </div>
        </div>

        <CreateListingForm />
      </div>
    </div>
  )
}
