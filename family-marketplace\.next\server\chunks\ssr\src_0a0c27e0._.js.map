{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/components/RegistrationForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter, useSearchParams } from 'next/navigation'\nimport Link from 'next/link'\n\ninterface FormData {\n  email: string\n  username: string\n  password: string\n  confirmPassword: string\n  firstName: string\n  lastName: string\n  userType: 'PRIVATE' | 'VENDOR'\n  // Vendor specific fields\n  businessName: string\n  businessDescription: string\n  website: string\n}\n\nexport default function RegistrationForm() {\n  const router = useRouter()\n  const searchParams = useSearchParams()\n\n  const [formData, setFormData] = useState<FormData>({\n    email: '',\n    username: '',\n    password: '',\n    confirmPassword: '',\n    firstName: '',\n    lastName: '',\n    userType: 'PRIVATE',\n    businessName: '',\n    businessDescription: '',\n    website: ''\n  })\n\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [success, setSuccess] = useState(false)\n\n  // Set initial user type based on URL parameter\n  useEffect(() => {\n    const type = searchParams.get('type')\n    if (type === 'vendor') {\n      setFormData(prev => ({ ...prev, userType: 'VENDOR' }))\n    } else if (type === 'private') {\n      setFormData(prev => ({ ...prev, userType: 'PRIVATE' }))\n    }\n  }, [searchParams])\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    setError(null)\n  }\n\n  const handleUserTypeChange = (userType: 'PRIVATE' | 'VENDOR') => {\n    setFormData(prev => ({\n      ...prev,\n      userType,\n      // Clear vendor fields if switching to private\n      businessName: userType === 'PRIVATE' ? '' : prev.businessName,\n      businessDescription: userType === 'PRIVATE' ? '' : prev.businessDescription,\n      website: userType === 'PRIVATE' ? '' : prev.website\n    }))\n  }\n\n  const validateForm = (): string | null => {\n    if (!formData.email || !formData.username || !formData.password) {\n      return 'Please fill in all required fields'\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      return 'Passwords do not match'\n    }\n\n    if (formData.password.length < 6) {\n      return 'Password must be at least 6 characters long'\n    }\n\n    if (formData.userType === 'VENDOR' && !formData.businessName) {\n      return 'Business name is required for vendor accounts'\n    }\n\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (!emailRegex.test(formData.email)) {\n      return 'Please enter a valid email address'\n    }\n\n    return null\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    const validationError = validateForm()\n    if (validationError) {\n      setError(validationError)\n      return\n    }\n\n    setIsSubmitting(true)\n    setError(null)\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email: formData.email,\n          username: formData.username,\n          password: formData.password,\n          firstName: formData.firstName || undefined,\n          lastName: formData.lastName || undefined,\n          userType: formData.userType,\n          vendorData: formData.userType === 'VENDOR' ? {\n            businessName: formData.businessName,\n            description: formData.businessDescription || undefined,\n            website: formData.website || undefined\n          } : undefined\n        })\n      })\n\n      const result = await response.json()\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Registration failed')\n      }\n\n      setSuccess(true)\n\n      // Redirect to login after successful registration, preserving redirect parameter\n      const redirectUrl = searchParams.get('redirect')\n      const loginUrl = redirectUrl\n        ? `/login?message=Registration successful! Please log in.&redirect=${encodeURIComponent(redirectUrl)}`\n        : '/login?message=Registration successful! Please log in.'\n\n      setTimeout(() => {\n        router.push(loginUrl)\n      }, 2000)\n\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Registration failed')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  if (success) {\n    return (\n      <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-lg p-8\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">🎉</div>\n          <h2 className=\"text-2xl font-bold text-green-600 mb-4\">\n            Registration Successful!\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Your {formData.userType.toLowerCase()} account has been created successfully.\n          </p>\n          <p className=\"text-sm text-gray-500\">\n            Redirecting to login page...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8\">\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Join Family Marketplace\n        </h1>\n        <p className=\"text-gray-600\">\n          Create your account and start selling with AI assistance\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* User Type Selection */}\n        <div className=\"space-y-4\">\n          <label className=\"block text-sm font-medium text-gray-700\">\n            Account Type *\n          </label>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <button\n              type=\"button\"\n              onClick={() => handleUserTypeChange('PRIVATE')}\n              className={`p-4 border-2 rounded-lg text-left transition-all ${\n                formData.userType === 'PRIVATE'\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-2xl mr-2\">👤</span>\n                <span className=\"font-semibold\">Private Seller</span>\n              </div>\n              <p className=\"text-sm text-gray-600\">\n                Sell personal items occasionally. Perfect for decluttering and one-time sales.\n              </p>\n            </button>\n\n            <button\n              type=\"button\"\n              onClick={() => handleUserTypeChange('VENDOR')}\n              className={`p-4 border-2 rounded-lg text-left transition-all ${\n                formData.userType === 'VENDOR'\n                  ? 'border-purple-500 bg-purple-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-2xl mr-2\">🏪</span>\n                <span className=\"font-semibold\">Business Vendor</span>\n              </div>\n              <p className=\"text-sm text-gray-600\">\n                Run a business with inventory management, analytics, and bulk tools.\n              </p>\n            </button>\n          </div>\n        </div>\n\n        {/* Basic Information */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              First Name\n            </label>\n            <input\n              type=\"text\"\n              value={formData.firstName}\n              onChange={(e) => handleInputChange('firstName', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"John\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Last Name\n            </label>\n            <input\n              type=\"text\"\n              value={formData.lastName}\n              onChange={(e) => handleInputChange('lastName', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Doe\"\n            />\n          </div>\n        </div>\n\n        {/* Account Details */}\n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Email Address *\n            </label>\n            <input\n              type=\"email\"\n              required\n              value={formData.email}\n              onChange={(e) => handleInputChange('email', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Username *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.username}\n              onChange={(e) => handleInputChange('username', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"johndoe\"\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Password *\n              </label>\n              <input\n                type=\"password\"\n                required\n                value={formData.password}\n                onChange={(e) => handleInputChange('password', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"••••••••\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Confirm Password *\n              </label>\n              <input\n                type=\"password\"\n                required\n                value={formData.confirmPassword}\n                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"••••••••\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Vendor-specific fields */}\n        {formData.userType === 'VENDOR' && (\n          <div className=\"space-y-4 p-4 bg-purple-50 rounded-lg border border-purple-200\">\n            <h3 className=\"font-semibold text-purple-800 mb-4\">\n              🏪 Business Information\n            </h3>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Business Name *\n              </label>\n              <input\n                type=\"text\"\n                required\n                value={formData.businessName}\n                onChange={(e) => handleInputChange('businessName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                placeholder=\"Your Business Name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Business Description\n              </label>\n              <textarea\n                rows={3}\n                value={formData.businessDescription}\n                onChange={(e) => handleInputChange('businessDescription', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                placeholder=\"Describe what your business sells...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Website (Optional)\n              </label>\n              <input\n                type=\"url\"\n                value={formData.website}\n                onChange={(e) => handleInputChange('website', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                placeholder=\"https://yourbusiness.com\"\n              />\n            </div>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n            <p className=\"text-red-700 text-sm\">{error}</p>\n          </div>\n        )}\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          disabled={isSubmitting}\n          className={`w-full py-3 px-4 rounded-md font-semibold text-white transition-all ${\n            formData.userType === 'VENDOR'\n              ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800'\n              : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800'\n          } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}\n        >\n          {isSubmitting ? (\n            <span className=\"flex items-center justify-center\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              Creating Account...\n            </span>\n          ) : (\n            `Create ${formData.userType === 'VENDOR' ? 'Vendor' : 'Private'} Account`\n          )}\n        </button>\n\n        {/* Login Link */}\n        <div className=\"text-center\">\n          <p className=\"text-gray-600\">\n            Already have an account?{' '}\n            <Link href=\"/login\" className=\"text-blue-600 hover:underline\">\n              Sign in here\n            </Link>\n          </p>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,OAAO;QACP,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,WAAW;QACX,UAAU;QACV,UAAU;QACV,cAAc;QACd,qBAAqB;QACrB,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,IAAI,SAAS,UAAU;YACrB,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAS,CAAC;QACtD,OAAO,IAAI,SAAS,WAAW;YAC7B,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAU,CAAC;QACvD;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,SAAS;IACX;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;gBACA,8CAA8C;gBAC9C,cAAc,aAAa,YAAY,KAAK,KAAK,YAAY;gBAC7D,qBAAqB,aAAa,YAAY,KAAK,KAAK,mBAAmB;gBAC3E,SAAS,aAAa,YAAY,KAAK,KAAK,OAAO;YACrD,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,QAAQ,EAAE;YAC/D,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,KAAK,YAAY,CAAC,SAAS,YAAY,EAAE;YAC5D,OAAO;QACT;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;YACpC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,QAAQ,KAAK,WAAW;wBAC3C,cAAc,SAAS,YAAY;wBACnC,aAAa,SAAS,mBAAmB,IAAI;wBAC7C,SAAS,SAAS,OAAO,IAAI;oBAC/B,IAAI;gBACN;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YAEX,iFAAiF;YACjF,MAAM,cAAc,aAAa,GAAG,CAAC;YACrC,MAAM,WAAW,cACb,CAAC,gEAAgE,EAAE,mBAAmB,cAAc,GACpG;YAEJ,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAE,WAAU;;4BAAqB;4BAC1B,SAAS,QAAQ,CAAC,WAAW;4BAAG;;;;;;;kCAExC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAK/B,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAA0C;;;;;;0CAG3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,qBAAqB;wCACpC,WAAW,CAAC,iDAAiD,EAC3D,SAAS,QAAQ,KAAK,YAClB,+BACA,yCACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,qBAAqB;wCACpC,WAAW,CAAC,iDAAiD,EAC3D,SAAS,QAAQ,KAAK,WAClB,mCACA,yCACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,SAAS;wCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC9D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC7D,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,eAAe;gDAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;oBAOnB,SAAS,QAAQ,KAAK,0BACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAInD,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,YAAY;wCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCACjE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAM;wCACN,OAAO,SAAS,mBAAmB;wCACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;wCACxE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;oBAOnB,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAKzC,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAW,CAAC,oEAAoE,EAC9E,SAAS,QAAQ,KAAK,WAClB,6FACA,mFACL,CAAC,EAAE,eAAe,kCAAkC,IAAI;kCAExD,6BACC,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;;;;;gCAAuE;;;;;;mCAIxF,CAAC,OAAO,EAAE,SAAS,QAAQ,KAAK,WAAW,WAAW,UAAU,QAAQ,CAAC;;;;;;kCAK7E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAgB;gCACF;8CACzB,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1E", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Visual%20Code/Family%20Market%20Place/family-marketplace/src/app/register/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSearchParams } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport RegistrationForm from '@/components/RegistrationForm'\nimport Link from 'next/link'\n\nexport default function RegisterPage() {\n  const searchParams = useSearchParams()\n  const type = searchParams.get('type')\n\n  // Get the user type from URL parameter\n  const getUserTypeFromUrl = () => {\n    if (type === 'vendor') return 'VENDOR'\n    if (type === 'private') return 'PRIVATE'\n    return null\n  }\n\n  const urlUserType = getUserTypeFromUrl()\n\n  return (\n    <div className=\"bg-gradient-to-br from-blue-50 to-purple-50 py-12\">\n\n      {/* Page Title with User Type Indicator */}\n      {urlUserType && (\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-6\">\n          <div className=\"text-center\">\n            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${\n              urlUserType === 'VENDOR'\n                ? 'bg-purple-100 text-purple-800'\n                : 'bg-blue-100 text-blue-800'\n            }`}>\n              <span className=\"mr-2\">\n                {urlUserType === 'VENDOR' ? '🏪' : '👤'}\n              </span>\n              Creating {urlUserType === 'VENDOR' ? 'Vendor' : 'Private Seller'} Account\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Registration Form */}\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <RegistrationForm />\n      </div>\n\n      {/* Benefits Section */}\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          {/* Private Seller Benefits */}\n          <div className=\"bg-white rounded-lg p-6 shadow-lg\">\n            <div className=\"flex items-center mb-4\">\n              <span className=\"text-3xl mr-3\">👤</span>\n              <h3 className=\"text-xl font-semibold\">Private Seller Benefits</h3>\n            </div>\n            <ul className=\"space-y-2 text-gray-600\">\n              <li className=\"flex items-center\">\n                <span className=\"text-green-500 mr-2\">✓</span>\n                Free listing creation with AI assistance\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-green-500 mr-2\">✓</span>\n                Smart photo analysis and categorization\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-green-500 mr-2\">✓</span>\n                Intelligent price suggestions\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-green-500 mr-2\">✓</span>\n                Simple messaging system\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-green-500 mr-2\">✓</span>\n                Perfect for decluttering and one-time sales\n              </li>\n            </ul>\n          </div>\n\n          {/* Vendor Benefits */}\n          <div className=\"bg-white rounded-lg p-6 shadow-lg border-2 border-purple-200\">\n            <div className=\"flex items-center mb-4\">\n              <span className=\"text-3xl mr-3\">🏪</span>\n              <h3 className=\"text-xl font-semibold\">Vendor Benefits</h3>\n              <span className=\"ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full\">\n                Business\n              </span>\n            </div>\n            <ul className=\"space-y-2 text-gray-600\">\n              <li className=\"flex items-center\">\n                <span className=\"text-purple-500 mr-2\">✓</span>\n                All private seller features included\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-purple-500 mr-2\">✓</span>\n                Bulk upload and inventory management\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-purple-500 mr-2\">✓</span>\n                Advanced analytics and insights\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-purple-500 mr-2\">✓</span>\n                Custom business storefront\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-purple-500 mr-2\">✓</span>\n                Priority customer support\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"text-purple-500 mr-2\">✓</span>\n                Multi-location inventory tracking\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAOe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,OAAO,aAAa,GAAG,CAAC;IAE9B,uCAAuC;IACvC,MAAM,qBAAqB;QACzB,IAAI,SAAS,UAAU,OAAO;QAC9B,IAAI,SAAS,WAAW,OAAO;QAC/B,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAU;;YAGZ,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,CAAC,oEAAoE,EACnF,gBAAgB,WACZ,kCACA,6BACJ;;0CACA,8OAAC;gCAAK,WAAU;0CACb,gBAAgB,WAAW,OAAO;;;;;;4BAC9B;4BACG,gBAAgB,WAAW,WAAW;4BAAiB;;;;;;;;;;;;;;;;;0BAOzE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,UAAgB;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;8CAExC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;gDAAQ;;;;;;;sDAGhD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;gDAAQ;;;;;;;sDAGhD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;gDAAQ;;;;;;;sDAGhD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;gDAAQ;;;;;;;sDAGhD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAOpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAK,WAAU;sDAAgF;;;;;;;;;;;;8CAIlG,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;sDAGjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;sDAGjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;sDAGjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;sDAGjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;sDAGjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D", "debugId": null}}]}